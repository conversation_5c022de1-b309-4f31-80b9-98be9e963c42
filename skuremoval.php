<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

require_once(__DIR__ . '/php/wp_check_access.php');
wp_checkAccess();

require_once(__DIR__ . '/php/catalogue_handler.php');
require_once(__DIR__ . '/php/woocommerce_api.php');


use CottonClassics\CatalogueHandler\CatalogueHandler;
use CottonClassics\WooCommerce_API\WooCommerce_API;

$c_handler = new CatalogueHandler();
$wcapi = new WooCommerce_API();

$products = $c_handler->getProducts();

$products = $c_handler->getProducts();
for ($i = count($products) - 1; $i >= 0; $i--) {
    if ($wcapi->SKUAlreadyUsed($products[$i]->Style)) {
        $c_handler->removeProduct($i);
    }
}

$response = [
    'status' => 'completed'
];

http_response_code(200);
echo json_encode($response);