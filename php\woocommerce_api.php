<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

namespace CottonClassics\WooCommerce_API;

require_once(__DIR__ . '/../../wp-load.php');

class WooCommerce_API {
    public function getCategories() {
        $categories = get_terms([
            'taxonomy'   => 'product_cat',
            'hide_empty' => false,
            'parent'     => 0
        ]);
    
        $category_list = [];
    
        if (!empty($categories) && !is_wp_error($categories)) {
            foreach ($categories as $category) {
                $category_list[] = [
                    'id'       => $category->term_id,
                    'name'     => $category->name,
                    'slug'     => $category->slug,
                    'children' => $this->getSubCategories($category->term_id)
                ];
            }
        }
        return $category_list;
    }
    
    private function getSubCategories($parent_id) {
        $sub_categories = get_terms([
            'taxonomy'   => 'product_cat',
            'hide_empty' => false,
            'parent'     => $parent_id
        ]);
    
        $sub_category_list = [];
    
        if (!empty($sub_categories) && !is_wp_error($sub_categories)) {
            foreach ($sub_categories as $sub_category) {
                $sub_category_list[] = [
                    'id'   => $sub_category->term_id,
                    'name' => $sub_category->name,
                    'slug' => $sub_category->slug
                ];
            }
        }
    
        return $sub_category_list;
    }

    public function SKUAlreadyUsed($sku) {
        $product_id = wc_get_product_id_by_sku($sku);
    
        if ($product_id) {
            return true;
        }
    
        return false;
    }    

    public function createProduct(
        $name,
        $price,
        $sale_price,
        $original_sku,
        $sku,
        $short_description,
        $description,
        $seo_title,
        $seo_keyword,
        $category_ids = [],
        $primary_category,
        $brand = '',
        $image_url = '',
        $tshirt_id = '',
        $price_rules = [] // [10 => "179", 50 => "169", 100 => "159"]
    ) {
        $product = new \WC_Product_Simple();
        $product->set_name($name);
        
        $product->set_sku($sku);
        $product->set_short_description($short_description);
        $product->set_description($description);
        $product->set_status('publish');
        $product->set_weight('0.1');
        
        $product_id = $product->save();
        
        update_post_meta($product_id, 'rank_math_title', $seo_title);
        update_post_meta($product_id, 'rank_math_focus_keyword', $seo_keyword);
        
        update_post_meta($product_id, 'technologie', 'digital');
        
        if (!empty($category_ids)) {
            $category_ids_unique = array_unique($category_ids);
            $valid_category_ids = [];
            
            foreach ($category_ids_unique as $cat_id) {
                $term = get_term_by('id', $cat_id, 'product_cat');
                if ($term) {
                    $valid_category_ids[] = $term->term_id;
                }
            }
            
            if (!empty($valid_category_ids)) {
                wp_set_object_terms($product_id, $valid_category_ids, 'product_cat');
            }
        }

        update_post_meta($product_id, 'rank_math_primary_product_cat', $primary_category);
        
        if (!empty($brand)) {
            $brand_term = get_term_by('name', $brand, 'product_brand');
            if (!$brand_term) {
                $new_brand = wp_insert_term($brand, 'product_brand');
                if (!is_wp_error($new_brand)) {
                    $brand_term_id = $new_brand['term_id'];
                }
            } else {
                $brand_term_id = $brand_term->term_id;
            }
            if (!empty($brand_term_id)) {
                wp_set_object_terms($product_id, $brand_term_id, 'product_brand');
            }
        }
        
        $purchase_link = 'https://www.cottonclassics.cz/catalogsearch/result?searchmode=quicksearch&q=' . $original_sku;
        update_post_meta($product_id, 'odkaz_k_nakupu', $purchase_link);
        
        $final_url = '';
        if (!empty($image_url)) {
            $upload_dir = wp_upload_dir();
            $image_data = @file_get_contents($image_url);
            if ($image_data) {
                $filename = basename($image_url);
                $file_path = $upload_dir['path'] . '/' . $filename;
                file_put_contents($file_path, $image_data);
                $wp_filetype = wp_check_filetype($filename, null);
                $attachment = [
                    'post_mime_type' => $wp_filetype['type'],
                    'post_title'     => sanitize_file_name($filename),
                    'post_content'   => '',
                    'post_status'    => 'inherit'
                ];
                $attach_id = wp_insert_attachment($attachment, $file_path, $product_id);
                require_once ABSPATH . 'wp-admin/includes/image.php';
                $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
                wp_update_attachment_metadata($attach_id, $attach_data);
                set_post_thumbnail($product_id, $attach_id);
        
                $final_url = $upload_dir['url'] . '/' . $filename;
            }
        }
        
        $designer_settings = ['open_designer' => 0];
        update_post_meta($product_id, 'product_designer_settings', $designer_settings);
        
        $wc_data = [
            [
                '_product_id'              => (string)$tshirt_id,
                '_disabled_product_design' => '',
                '_product_title_img'       => $name . '::' . $final_url,
                'campaign_profit'          => '',
                'campaign_end'             => '',
                'campaign_units'           => '',
                'campaign_start'           => '',
                'campaign_options'         => ''
            ]
        ];
        update_post_meta($product_id, 'wc_productdata_options', $wc_data);
        
        if (!empty($price_rules) && is_array($price_rules)) {
            update_post_meta($product_id, '_fixed_price_rules', $price_rules);
        }

        update_post_meta($product_id, '_sale_price', $sale_price);
        update_post_meta($product_id, '_price', $sale_price);
        update_post_meta($product_id, '_regular_price', $price);

        // Mirror categories, do not call constructor
        require_once(__DIR__ . '/../../tshirtecommerce/platforms/wordpress/admin/settings.php');
        $reflection = new \ReflectionClass('P9f_addons_settings');
        $obj = $reflection->newInstanceWithoutConstructor();
        $obj->update_product_categories_json();
        //

        $parts = [];

        foreach ($category_ids as $cat_id) {
            $term = get_term_by('id', $cat_id, 'product_cat');
            if ($term && $term->parent == 0 && $term->term_id != $primary_category) {
                $parts[] = $term->name;
            }
        }

        $primary = get_term_by('id', $primary_category, 'product_cat');
        if ($primary) {
            $parts[] = $primary->name;
        }

        foreach ($category_ids as $cat_id) {
            $term = get_term_by('id', $cat_id, 'product_cat');
            if ($term && $term->parent == $primary_category) {
                $parts[] = $term->name;
            }
        }

        if (!empty($parts)) {
            $product_type_google = implode(' > ', $parts);
            update_post_meta($product_id, '_product_type_google', $product_type_google);
        }

        return $product_id;
    }
}