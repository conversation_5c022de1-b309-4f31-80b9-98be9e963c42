<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

namespace CottonClassics\Functions;

class Functions {
    public function loadConfigFile($filename) {
        $config_file = __DIR__ . '/../config/' . $filename;
        if (!file_exists($config_file)) {
            die("Nelze nalézt konfigurační soubor!: $config_file");
        }

        $config = parse_ini_file($config_file, true);

        return $config;
    }

    public function dbPDO() {
        $config = $this->loadConfigFile('db.conf');

        try {
            $pdo = new \PDO(
                "mysql:host={$config['database']['host']};dbname={$config['database']['name']}",
                $config['database']['user'],
                $config['database']['password']
            );
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
            return $pdo;
        } catch (\PDOException $e) {
            die("Chyba při připojení k databázi: " . $e->getMessage());
        }
    }

    function getNomen($local_id, $local_color, $local_size) {
        $conn = $this->dbPDO();
        $sql = "SELECT nomen FROM 6tkk_cotton_pair WHERE local_id=:local_id AND local_color=:local_color AND local_size=:local_size";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':local_id' => $local_id, ':local_color' => $local_color, ':local_size' => $local_size]);

        $result = $stmt->fetchAll(\PDO::FETCH_ASSOC);

        if (count($result) == 1) {
            foreach ($result as $row) {
                return $row["nomen"];
            }
        }
        elseif(count($result) > 1) {
            return "";
        }
        else {
            return "";
        }
    }
}