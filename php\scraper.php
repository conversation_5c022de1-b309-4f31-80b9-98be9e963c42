<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

namespace CottonClassics\Scraper;

use CottonClassics\Functions\Functions;
require_once(__DIR__ . '/functions.php');

class Scraper {
    private $functions;
    private $ftp_conn_id;

    function __construct() {
        $this->functions = new Functions();
    }

    private function connectFTP() {
        $config = $this->functions->loadConfigFile("ftp.conf");

        $ftp_server = $config['ftp']['server'];
        $ftp_user = $config['ftp']['user'];
        $ftp_password = $config['ftp']['password'];

        $this->ftp_conn_id = ftp_connect($ftp_server);

        if (!$this->ftp_conn_id) {
            die("Připojení k FTP serveru selhalo!: $ftp_server");
        }

        if (!ftp_login($this->ftp_conn_id, $ftp_user, $ftp_password)) {
            ftp_close($this->ftp_conn_id);
            die("Sel<PERSON> autentifikace u FTP serveru!");
        }

        return true;
    }

    public function getProductCatalogue() {
        if (!$this->ftp_conn_id) {
            $this->connectFTP();
        }
    
        $save_to = __DIR__ . '/../temp/catalogue.xlsx';
        $downloaded_date = '';
    
        for ($i = 0; $i < 12; $i++) {
            $catalogue_file = "xlsx-data/" . date('Y-m', strtotime("-$i months")) . "/Article_Export_" . date('Y-m', strtotime("-$i months")) . "_(CZK).xlsx";
    
            if (ftp_get($this->ftp_conn_id, $save_to, $catalogue_file, FTP_BINARY)) {
                $downloaded_date = date('Y-m', strtotime("-$i months"));
                break;
            }
    
            if ($i == 11) {
                die("Stažení katalogu selhalo!: $catalogue_file");
            }
        }
    
        if(!$this->convertXLSXToJSON($save_to, __DIR__ . '/../temp/catalogue')) {
            die("Nelze převést data na strojově čitelný formát!: $save_to");
        }
    
        return $downloaded_date;
    }
    

    private function convertXLSXToJSON($source, $destDir)
    {
        $zip = new \ZipArchive();
        if ($zip->open($source) !== TRUE) {
            echo "Nepodařilo se otevřít XLSX soubor: $source\n";
            return false;
        }

        $workbookXml = $zip->getFromName('xl/workbook.xml');
        if (!$workbookXml) {
            echo "Nepodařilo se načíst xl/workbook.xml\n";
            $zip->close();
            return false;
        }
        $workbookXmlObj = simplexml_load_string($workbookXml);
        $namespaces = $workbookXmlObj->getNamespaces(true);

        $relsXml = $zip->getFromName('xl/_rels/workbook.xml.rels');
        if (!$relsXml) {
            echo "Nepodařilo se načíst xl/_rels/workbook.xml.rels\n";
            $zip->close();
            return false;
        }
        $relsXmlObj = simplexml_load_string($relsXml);

        $ridToTarget = [];
        foreach ($relsXmlObj->Relationship as $rel) {
            $rId = (string)$rel['Id'];
            $target = (string)$rel['Target'];
            $ridToTarget[$rId] = $target;
        }

        $sheets = $workbookXmlObj->sheets->sheet;

        $sharedStringsArray = [];
        $sharedStrings = $zip->getFromName('xl/sharedStrings.xml');
        if ($sharedStrings) {
            $sharedStringsXml = simplexml_load_string($sharedStrings);
            foreach ($sharedStringsXml->si as $stringItem) {
                $sharedStringsArray[] = (string)$stringItem->t;
            }
        }

        if (!is_dir($destDir)) {
            mkdir($destDir, 0777, true);
        }

        foreach ($sheets as $sheet) {
            $sheetName = (string)$sheet['name'];
            $rIdAttr   = $sheet->attributes($namespaces['r']);
            $rId       = (string)$rIdAttr['id'];

            if (!isset($ridToTarget[$rId])) {
                echo "Nepodařilo se najít vazbu (relationship) pro list: $sheetName\n";
                continue;
            }

            $sheetFile = 'xl/' . $ridToTarget[$rId];
            $sheetData = $zip->getFromName($sheetFile);
            if (!$sheetData) {
                echo "Nepodařilo se načíst list: $sheetName\n";
                continue;
            }

            $sheetXml = simplexml_load_string($sheetData);
            $rows = $sheetXml->sheetData->row;
            $data = [];
            $headers = [];
            $isHeader = true;

            foreach ($rows as $row) {
                $tempRow = [];

                foreach ($row->c as $cell) {
                    $value = (string)$cell->v;
                    $type  = (string)$cell['t'];

                    if ($type === 's' && isset($sharedStringsArray[$value])) {
                        $value = $sharedStringsArray[$value];
                    } elseif (empty($value)) {
                        $value = null;
                    }

                    $cellRef       = (string)$cell['r'];
                    $columnLetters = preg_replace('/\d/', '', $cellRef);
                    $colIndex      = $this->columnLettersToIndex($columnLetters);

                    $tempRow[$colIndex] = $value;
                }

                if ($isHeader) {
                    $headers = $tempRow;
                    $isHeader = false;
                } else {
                    $rowData = [];
                    foreach ($headers as $i => $headerName) {
                        $rowData[$headerName] = array_key_exists($i, $tempRow) ? $tempRow[$i] : null;
                    }
                    $data[] = $rowData;
                }
            }

            $safeSheetName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $sheetName);
            $jsonFileName = "{$destDir}/{$safeSheetName}.json";

            file_put_contents($jsonFileName, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }

        $zip->close();
        unlink($source);
        
        return true;
    }

    private function columnLettersToIndex($letters)
    {
        $letters = strtoupper($letters);
        $num = 0;
        for ($i = 0; $i < strlen($letters); $i++) {
            $num = $num * 26 + (ord($letters[$i]) - ord('A')) + 1;
        }
        return $num - 1;
    }

    public function getImageGUID($guid)
    {
        if (!$this->ftp_conn_id) {
            $this->connectFTP();
        }

        $remote_path = 'picture_db/GUID/Alle/' . $guid;

        $destDir    = __DIR__ . '/../temp/pictures';
        $local_path = $destDir . '/' . $guid;

        if (!is_dir($destDir)) {
            mkdir($destDir, 0777, true);
        }

        if (!ftp_get($this->ftp_conn_id, $local_path, $remote_path, FTP_BINARY)) {
            die("Stažení obrázku selhalo!: $remote_path");
        }

        $info = getimagesize($local_path);
        if ($info === false) {
            die("Nelze načíst informace o obrázku: $guid");
        }
        [$width, $height, $image_type] = $info;

        $targetSize = 1024;

        if ($width > $targetSize || $height > $targetSize) {
            $scale = min($targetSize / $width, $targetSize / $height);
            $newW  = (int) round($width  * $scale);
            $newH  = (int) round($height * $scale);
        } else {
            $newW = $width;
            $newH = $height;
        }

        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $src = imagecreatefromjpeg($local_path);
                break;
            case IMAGETYPE_PNG:
                $src = imagecreatefrompng($local_path);
                break;
            case IMAGETYPE_GIF:
                $src = imagecreatefromgif($local_path);
                break;
            default:
                die("Nepodporovaný typ obrázku: $guid");
        }

        if (!$src) {
            die("Nelze otevřít obrázek: $guid");
        }

        $dest  = imagecreatetruecolor($targetSize, $targetSize);
        $white = imagecolorallocate($dest, 255, 255, 255);
        imagefill($dest, 0, 0, $white);

        $offsetX = (int) round(($targetSize - $newW) / 2);
        $offsetY = (int) round(($targetSize - $newH) / 2);
        imagecopyresampled($dest, $src, $offsetX, $offsetY, 0, 0, $newW, $newH, $width, $height);

        $target_path = __DIR__ . '/../temp/pictures/' . $guid;

        switch ($image_type) {
            case IMAGETYPE_JPEG:
                imagejpeg($dest, $target_path, 100);
                break;
            case IMAGETYPE_PNG:
                imagepng($dest, $target_path);
                break;
            case IMAGETYPE_GIF:
                imagegif($dest, $target_path);
                break;
        }

        imagedestroy($src);
        imagedestroy($dest);

        return $guid;
    }

    public function getImageSizeTableStyleID($style_id) {
        if (!$this->ftp_conn_id) {
            $this->connectFTP();
        }
    
        $pre_id = strstr($style_id, '.', true);
        $aft_id = ltrim(strstr($style_id, '.'), '.');
    
        $remote_path = "picture_db/SystemischeDateinamen/Lizenzfrei/$pre_id/$aft_id";
        $gt_path = $remote_path . "/GT";
    
        $items = ftp_nlist($this->ftp_conn_id, $remote_path);
        
        if (!$items || !in_array($gt_path, $items)) {
            return false;
        }
    
        $files = ftp_nlist($this->ftp_conn_id, $gt_path);
        
        if (!$files || empty($files)) {
            return false;
        }

        $first_file = $files[0];
    
        $destDir = __DIR__ . "/../temp/sizetable_pictures";
        if (!is_dir($destDir)) {
            mkdir($destDir, 0777, true);
        }
    
        $extension = pathinfo($first_file, PATHINFO_EXTENSION);  
        $local_path = $destDir . '/' . $style_id . '.' . $extension;
    
        if (!ftp_get($this->ftp_conn_id, $local_path, $first_file, FTP_BINARY)) {
            return false;
        }
    
        return basename($local_path);
    }    
}