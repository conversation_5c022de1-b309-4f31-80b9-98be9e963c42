<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

require_once(__DIR__ . '/php/wp_check_access.php');
wp_checkAccess();

session_start();

header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

if(!isset($_SESSION['actual_position'])) {
    $_SESSION['actual_position'] = 1;
}

require_once(__DIR__ . '/php/interface_handler.php');
$ihandler = new \CottonClassics\InterfaceHandler\InterfaceHandler();
?>

<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <title>Cotton Classics - Import</title>
</head>

<body>
    <!-- Loading overlay -->
    <div id="loadingOverlay" class="d-none position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 d-flex justify-content-center align-items-center" style="z-index: 999999;">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Načítání...</span>
        </div>
    </div>
    <!-- -->
    <div class="text-center">
        <h2 class="mt-4">Produkt na pozici <?php echo $ihandler->html_ProductNumber(); ?></h2>
        <div class="ms-4 mt-3">
            <button class="btn btn-outline-danger btn-sm" onclick="remove();">❌ Vyřadit</button>
            <button class="btn btn-secondary btn-sm" onclick="moveb();">⏮️ Dozadu</button>
            <button class="btn btn-secondary btn-sm" onclick="movef();">⏭️ Dopředu</button>
            <button class="btn btn btn-outline-success btn m-3" onclick="sendData();">📥 Importovat</button>
        </div>
        <script>
            window.addEventListener('DOMContentLoaded', () => {
                const paramsToRemove = [
                    'mode',
                    'design',
                    'color',
                    'categories',
                    'main_category',
                    'wc_short'
                ];

                const url = new URL(window.location.href);
                let modified = false;

                paramsToRemove.forEach(param => {
                    if (url.searchParams.has(param)) {
                        url.searchParams.delete(param);
                        modified = true;
                    }
                });

                if (modified) {
                    window.history.replaceState({}, '', url);
                }
            });

            function remove() {
                let res = confirm("Opravdu chcete vyřadit tento produkt? Tato akce je nevratná, a ubere jedno číslo z celkového počtu produktů připravených pro import.\n\nProdukt bude znovu zařazen poté, co provedete opětovné stažení produktové databáze na hlavní stránce.\n\nPokud byl tento produkt naimportován do WooCommerce, nemusíte mít obavy že by tam byl odstraněn.");
                if(res) {
                    showLoading();
                    window.location.href = "remove.php";
                }
            }

            function moveb() {
                showLoading();
                window.location.href = "move.php?direction=backward";
            }

            function movef() {
                showLoading();
                window.location.href = "move.php?direction=forward";
            }

            function gen(id, hexcolor) {
                let selectedCategories = [];
                document.querySelectorAll('.form-check-input[type="checkbox"]:checked').forEach(function(checkbox) {
                    let categoryId = checkbox.id.split("cat_")[1];
                    if (categoryId) {
                        selectedCategories.push(categoryId);
                    }
                });

                let mainCategory = document.querySelector('input[name="default_category"]:checked');
                let mainCategoryId = null;
                if (mainCategory) {
                    let idMatch = mainCategory.id.match(/default_cat_(\d+)/);
                    if (idMatch) {
                        mainCategoryId = idMatch[1];
                    }
                }

                let shortDescElement = document.getElementById('short_description');
                let shortDescContent = shortDescElement ? shortDescElement.value : '';

                let url = "import.php?mode=gen&design=" + encodeURIComponent(id) + "&color=" + encodeURIComponent(hexcolor);

                if (selectedCategories.length > 0) {
                    url += "&categories=" + encodeURIComponent(selectedCategories.join(','));
                }

                if (mainCategoryId) {
                    url += "&main_category=" + encodeURIComponent(mainCategoryId);
                }

                if (shortDescContent) {
                    url += "&wc_short=" + encodeURIComponent(shortDescContent);
                }

                showLoading();
                window.location.href = url;
            }

            function showLoading() {
                document.getElementById("loadingOverlay").classList.remove("d-none");
            }

            function hideLoading() {
                document.getElementById("loadingOverlay").classList.add("d-none");
            }

            /* Data send */
            function sendData() {
                let formData = new FormData();

                formData.append('title', document.getElementById('title').value);
                formData.append('price', document.getElementById('price').value);
                formData.append('price_sale', document.getElementById('price_sale').value);
                formData.append('sku', document.getElementById('sku').value);
                formData.append('short_description', document.getElementById('short_description').value);
                formData.append('long_description', document.getElementById('long_description').value);
                formData.append('seo_title', document.getElementById('seo_title').value);
                formData.append('seo_keywords', document.getElementById('seo_word').value);
                formData.append('categories', JSON.stringify(getSelectedCategories()));
                formData.append('sizes', JSON.stringify(getSelectedSizes()));
                formData.append('colors', JSON.stringify(getSelectedColors()));
                formData.append('quantityDiscounts', JSON.stringify(getQuantityDiscounts()));
                formData.append('minimum_quantity', document.getElementById('minimum_quantity').value);

                let mainCategory = document.querySelector('input[name="default_category"]:checked');
                if (mainCategory) {
                    let idMatch = mainCategory.id.match(/default_cat_(\d+)/);
                    if (idMatch) {
                        formData.append('main_category', idMatch[1]);
                    }
                }

                let image = getImage();
                if (image) {
                    if (typeof image === 'string') {
                        formData.append('image_url', image);
                    } else {
                        formData.append('image_file', image);
                    }
                }

                showLoading();

                fetch('process_import.php', { 
                    method: 'POST', 
                    body: formData 
                })
                .then(response => response.json())
                .then(result => {
                    hideLoading();
                    if (result.status == 'success') {
                        let w = window.screen.availWidth;
                        let h = window.screen.availHeight;

                        window.open(
                            `/tshirtecommerce/admin/index.php?/product/edit/${result.tshirt_id}`,
                            '_blank',
                            `width=${w},height=${h},top=0,left=0,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,noopener,noreferrer`
                        );

                        setTimeout(() => {
                            showLoading();
                            location.href = location.href;
                        }, 4000);
                    }
                    else if (result.status == 'invalid_sku') {
                        alert('Nemůžete importovat tento produkt, protože zadané SKU se shoduje s SKU již existujícího WC produktu.');
                    }
                    else {
                        alert('Import selhal. Kontaktuje IT technika.');
                    }
                })
                .catch(error => {
                    hideLoading();
                    alert('Import selhal. Kontaktuje IT technika.');
                });
            }

            function getSelectedCategories() {
                let categories = [];
                document.querySelectorAll('.form-check-input:checked').forEach(function(checkbox) {
                    let categoryId = checkbox.id.split("cat_")[1];
                    if (categoryId) {
                        categories.push(categoryId);
                    }
                });
                return categories;
            }

            function getSelectedSizes() {
                let sizes = [];
                document.querySelectorAll('.form-check-input[id^="sizecheckbox"]').forEach(function(checkbox) {
                    let sizeValue = checkbox.id.split("sizecheckbox_")[1];
                    let sizeTextElement = document.getElementById('sizetext_' + sizeValue);

                    if (checkbox.checked) {
                        sizes.push(sizeTextElement ? sizeTextElement.value : "null");
                    } else {
                        sizes.push("null");
                    }
                });
                return sizes;
            }

            function getSelectedColors() {
                let colors = [];
                document.querySelectorAll('.form-check-input[id^="colorcheckbox"]:checked').forEach(function(checkbox) {
                    let colorId = checkbox.id.replace("colorcheckbox_", "");
                    colors.push(colorId);
                });
                return colors;
            }

            function getImage() {
                let image = document.getElementById("imagePreview").src;
                if (!image || image === "") {
                    const fileInput = document.getElementById("fileUpload");
                    if (fileInput.files.length > 0) {
                        return fileInput.files[0];
                    } else {
                        return null;
                    }
                }

                return image;
            }

            function getQuantityDiscounts() {
                let discounts = [];
                document.querySelectorAll('#pricingTable tr').forEach(function(row) {
                    let from = row.querySelector('[id^="quantitypricefrom_"]').value;
                    let to = row.querySelector('[id^="quantitypriceto_"]').value;
                    let price = row.querySelector('[id^="quantityprice_"]').value;
                    
                    if (from && to && price) {
                        discounts.push({
                            from: from,
                            to: to,
                            price: price
                        });
                    }
                });
                return discounts;
            }

            document.addEventListener('DOMContentLoaded', function () {
                function checkParentCheckboxes(el) {
                    const parent = el.closest('div.ms-3')?.previousElementSibling?.querySelector('input[type="checkbox"]');
                    if (parent && !parent.checked) {
                        parent.checked = true;
                        checkParentCheckboxes(parent);
                    }
                }

                document.querySelectorAll('input.form-check-input[type="checkbox"]').forEach(cb => {
                    cb.addEventListener('change', function () {
                        if (this.checked) {
                            checkParentCheckboxes(this);
                        }
                    });
                });

                document.querySelectorAll('input.form-check-input[type="radio"][name="default_category"]').forEach(rb => {
                    rb.addEventListener('change', function () {
                        const relatedCheckbox = document.querySelector('#cat_' + this.id.replace('default_cat_', ''));
                        if (relatedCheckbox && !relatedCheckbox.checked) {
                            relatedCheckbox.checked = true;
                            checkParentCheckboxes(relatedCheckbox);
                        }
                    });
                });
            });
            /* End */
        </script>
        <div class="d-flex align-items-start m-5">
            <div class="d-flex flex-column">
                <?php echo $ihandler->html_ProductImage(); ?>

                <!-- WC & T-shirt Kategorie pod obrázkem -->
                <div class="mt-3 w-100 text-start">
                    <h4 class="mb-3">WC & T-shirt Kategorie:</h4>
                    <div class="border rounded p-3 shadow-sm" style="background-color: #f8f9fa; max-width: 350px;">
                        <?php echo $ihandler->html_Categories(); ?>
                    </div>                    
                </div>
            </div>

            <div class="d-flex flex-column align-items-start w-100 ms-4">

                <!-- Název, Cena, Cena po slevě -->
                <div class="d-flex w-100 align-items-center mb-3">
                    <?php echo $ihandler->html_TitlePriceSalePrice(); ?>
                </div>

                <hr style="height: 4px; width: 100%; background-color: black; border: none; margin: 20px 0;">

                <!-- WC SKU -->
                <div class="d-flex w-100 align-items-start mt-3 mb-3">
                    <?php echo $ihandler->html_WCSKU(); ?>
                </div>

                <!-- WC Krátký popis, WC Popis produktu -->
                <div class="w-100 mt-4">
                    <?php echo $ihandler->html_ShortDescLongDesc(); ?>
                </div>

                <hr style="height: 4px; width: 100%; background-color: black; border: none; margin: 20px 0;">

                <!-- SEO -->
                <table style="border-collapse: collapse; width: auto;">
                    <?php echo $ihandler->html_SEOTitleSEOWord(); ?>
                </table>

                <hr style=ight: 4px; width: 100%; background-color: black; border: none; margin: 20px 0;">

                <!-- T-shirt Barvy -->
                <div class="d-flex flex-column align-items-start">
                    <h4 class="mb-3 text-start">T-shirt Barvy:</h4>
                        <?php echo $ihandler->html_TshirtColors(); ?>
                    </div>
                </div>

                <!-- T-shirt Velikosti -->
                <div class="d-flex flex-column align-items-start mt-5">
                    <h4 class="mb-3 text-start">T-shirt Velikosti:</h4>
                        <?php echo $ihandler->html_TshirtSizes(); ?>
                </div>

                <!-- T-shirt Tabulka velikostí -->
                <div class="d-flex flex-column align-items-start mt-5">
                    <?php echo $ihandler->html_SizeTableFile(); ?>
                </div>

                <!-- T-shirt Mnozstevni slevy -->
                <div class="d-flex flex-column align-items-start mt-5">
                    <h4 class="mb-3 text-start">T-shirt Množstevní slevy:</h4>
                    <table class="table">
                        <?php echo $ihandler->html_QuantityPriceTable(); ?>
                    </table>
                    <button class="btn btn-primary" onclick="addRow()">Přidat řádek</button>
                </div>

                <!-- T-shirt Minimum purchase quantity -->
                <div class="d-flex flex-column w-100 align-items-start mt-5 mb-3">
                    <h4 class="mb-2">T-shirt Minimum purchase quantity:</h4>
                    <input id="minimum_quantity" value="1" type="text" style="font-size: 1.25rem; width: 100px;">
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>
</body>

<style>
    #productImageCarousel .carousel-item {
        transition: none !important;
    }
</style>

</html>