<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

require_once(__DIR__ . '/php/wp_check_access.php');
wp_checkAccess();

session_start();
$position = $_SESSION['actual_position'] - 1;

require_once(__DIR__ . '/php/catalogue_handler.php');
use CottonClassics\CatalogueHandler\CatalogueHandler;

$c_handler = new CatalogueHandler();
$actual_product = $c_handler->getProduct($position);

require_once(__DIR__ . '/php/woocommerce_api.php');
require_once(__DIR__ . '/php/tshirtecommerce_api.php');

use CottonClassics\WooCommerce_API\WooCommerce_API;
use CottonClassics\Tshirtecommerce_API\Tshirtecommerce_API;

$wcapi = new WooCommerce_API();
$tsapi = new Tshirtecommerce_API();

require_once(__DIR__ . '/php/functions.php');
use CottonClassics\Functions\Functions;
$functions = new Functions();

function uploadImage($image_file, $upload_dir)
{
    $timestamp = time();
    $hashed_name = hash('sha256', $timestamp . rand()) . '.' . pathinfo($image_file['name'], PATHINFO_EXTENSION);
    $upload_file = $upload_dir . $hashed_name;

    if (move_uploaded_file($image_file['tmp_name'], $upload_file)) {
        return $hashed_name;
    } else {
        return null;
    }
}

function uploadBase64Image($image_url, $upload_dir)
{
    $image_data = base64_decode(substr($image_url, strpos($image_url, ",") + 1));

    preg_match('/data:image\/(.*?);/', $image_url, $matches);
    $image_extension = $matches[1] ?? 'png';

    $timestamp = time();
    $hashed_name = hash('sha256', $timestamp . rand()) . '.' . $image_extension;
    $upload_file = $upload_dir . $hashed_name;

    if (file_put_contents($upload_file, $image_data)) {
        return $hashed_name;
    } else {
        return null;
    }
}

function uploadImageFromUrl($image_url, $upload_dir)
{
    $image_data = file_get_contents($image_url);
    $image_extension = pathinfo($image_url, PATHINFO_EXTENSION);

    $timestamp = time();
    $hashed_name = hash('sha256', $timestamp . rand()) . '.' . $image_extension;
    $upload_file = $upload_dir . $hashed_name;

    if (file_put_contents($upload_file, $image_data)) {
        return $hashed_name;
    } else {
        return null;
    }
}

function ensureUploadDirectoryExists($upload_dir)
{
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
}

// SENT DATA
$sku = $_POST['sku'] ?? null;

if ($wcapi->SKUAlreadyUsed($sku)) {
    $response = [
        'status' => 'invalid_sku',
    ];

    http_response_code(200);
    echo json_encode($response);
    exit;
}

$title = $_POST['title'] ?? null;
$price = $_POST['price'] ?? null;
$price_sale = $_POST['price_sale'] ?? null;
$short_description = $_POST['short_description'] ?? null;
$long_description = $_POST['long_description'] ?? null;
$seo_title = $_POST['seo_title'] ?? null;
$seo_keywords = $_POST['seo_keywords'] ?? null;
$minimum_quantity = $_POST['minimum_quantity'] ?? null;
$categories = isset($_POST['categories']) ? json_decode(stripslashes($_POST['categories'])) : [];
$main_category = $_POST['main_category'] ?? null;
$sizes = isset($_POST['sizes']) ? json_decode(stripslashes($_POST['sizes'])) : [];
$colors = isset($_POST['colors']) ? json_decode(stripslashes($_POST['colors'])) : [];
$quantityDiscounts = isset($_POST['quantityDiscounts']) ? json_decode(stripslashes($_POST['quantityDiscounts'])) : [];

$image_url = $_POST['image_url'] ?? null;
$image_file = $_FILES['image_file'] ?? null;

$upload_dir_sizetable = '../tshirtecommerce/uploaded/cottonimported/sizetables/';

ensureUploadDirectoryExists($upload_dir_sizetable);

$uploaded_image_name = null;
if ($image_file) {
    $uploaded_image_name = uploadImage($image_file, $upload_dir_sizetable);
} elseif ($image_url && strpos($image_url, 'data:image') === 0) {
    $uploaded_image_name = uploadBase64Image($image_url, $upload_dir_sizetable);
} elseif ($image_url) {
    $uploaded_image_name = uploadImageFromUrl($image_url, $upload_dir_sizetable);
}

//////////////

function getColorHex($colors, $actual_product)
{
    $color_map = [];

    foreach ($colors as $color_name) {
        foreach ($actual_product->Variants as $variant) {
            if (strtolower($variant->Color) === strtolower($color_name)) {
                $color_map[$color_name] = $variant->RGBColor;
                break;
            }
        }
    }

    return $color_map;
}

function moveImages($actual_product, array $colors, string $aiView = '')
{
    $upload_dir = '../tshirtecommerce/uploaded/cottonimported/images/';
    $image_urls = [];

    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    foreach ($actual_product->Variants as $variant) {
        if (!in_array($variant->Color, $colors, true)) {
            continue;
        }

        $image_name = $variant->Image;

        if ($aiView !== '') {
            $info = pathinfo($image_name);
            $image_name = $info['filename'] . '_' . $aiView . 'ai.png';
        }

        $source_path = 'temp/pictures/' . $image_name;
        if (!file_exists($source_path)) {
            continue;
        }

        $extension = pathinfo($image_name, PATHINFO_EXTENSION);
        $hashed_name = hash('sha256', time() . rand()) . '.' . $extension;
        $target_path = $upload_dir . $hashed_name;

        if (rename($source_path, $target_path)) {
            $image_urls[] = 'https://vytvorsipotisk.cz/tshirtecommerce/uploaded/cottonimported/images/' . $hashed_name;
        }
    }

    return $image_urls;
}

$images_front = moveImages($actual_product, $colors, "");
$images_back = moveImages($actual_product, $colors, "back");
$images_left = moveImages($actual_product, $colors, "left");
$images_right = moveImages($actual_product, $colors, "right");

// T-shirt format
$tshirt_quantity_discounts = [
    'min_quantity' => [],
    'max_quantity' => [],
    'price' => []
];
foreach ($quantityDiscounts as $item) {
    $tshirt_quantity_discounts['min_quantity'][] = (int) $item->from;
    $tshirt_quantity_discounts['max_quantity'][] = (int) $item->to;
    $tshirt_quantity_discounts['price'][] = (int) $item->price;
}
// End

// WooCommerce format
$woocommerce_quantity_discounts = [];
foreach ($quantityDiscounts as $item) {
    $max_quantity = (int) $item->to;
    $wc_price = (int) $item->price;
    if ($max_quantity > 0) {
        $woocommerce_quantity_discounts[$max_quantity] = (string) $wc_price;
    }
}
// End

$sizes_no_null = array_values(array_filter($sizes, fn($v) => $v !== 'null'));

$tshirt_given_id = $tsapi->createProduct(
    $sku,
    $title,
    $price,
    $long_description,
    $short_description,
    $images_front,
    $images_back,
    $images_left,
    $images_right,
    $minimum_quantity,
    '<p><img src="https://vytvorsipotisk.cz/tshirtecommerce/uploaded/cottonimported/sizetables/' . $uploaded_image_name . '" alt="Obrazek"/></p>',
    $price_sale,
    $sizes_no_null,
    $tshirt_quantity_discounts,
    getColorHex($colors, $actual_product),
);


$wc_product_id = $wcapi->createProduct(
    $title,
    $price,
    $price_sale,
    $actual_product->Style,
    $sku,
    $short_description,
    $long_description,
    $seo_title,
    $seo_keywords,
    $categories,
    $main_category,
    $actual_product->Manufacturer,
    $images_front[0],
    $tshirt_given_id,
    $woocommerce_quantity_discounts,
);

// Pair nomens for auto ordering and stock info ---------------------------------------------------->
if (isset($actual_product->SizeColorSKUs) && is_object($actual_product->SizeColorSKUs)) {
    $sizeMap = [];
    if (is_array($actual_product->Sizes) && is_array($sizes)) {
        foreach ($actual_product->Sizes as $idx => $origSize) {
            if (isset($sizes[$idx]) && $sizes[$idx] !== 'null') {
                $sizeMap[$origSize] = $sizes[$idx];
            }
        }
    }

    $allowedHex = [];
    foreach ($actual_product->Variants as $v) {
        if (in_array($v->Color, $colors, true)) {
            $allowedHex[] = strtolower($v->RGBColor);
        }
    }

    $pdo = $functions->dbPDO();
    $stmt = $pdo->prepare(
        "INSERT INTO `6tkk_cotton_pair` (local_id, local_color, local_size, nomen)
         VALUES (:local_id, :local_color, :local_size, :nomen)"
    );

    foreach ($actual_product->SizeColorSKUs as $nomen => $info) {
        if (!is_array($info) || count($info) !== 2) {
            continue;
        }

        [$hex, $origSize] = $info;

        if (!in_array(strtolower($hex), $allowedHex, true)) {
            continue;
        }

        if (!isset($sizeMap[$origSize])) {
            continue;
        }

        $stmt->execute([
            'local_id' => $wc_product_id,
            'local_color' => $hex,
            'local_size' => $sizeMap[$origSize],
            'nomen' => $nomen
        ]);
    }
}
// End ---------------------------------------------------->

$c_handler->removeProduct($position);

$response = [
    'status' => 'success',
    'tshirt_id' => $tshirt_given_id,
];

http_response_code(200);
echo json_encode($response);