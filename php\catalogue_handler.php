<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

namespace CottonClassics\CatalogueHandler;

class CatalogueHandler
{
    public function mergeProductData(): array
    {
        $products        = json_decode(file_get_contents(__DIR__ . '/../temp/catalogue/Style_List.json'), false);
        $skuList         = json_decode(file_get_contents(__DIR__ . '/../temp/catalogue/SKU-List.json'), false);
        $colourTemplates = json_decode(file_get_contents(__DIR__ . '/../temp/catalogue/Color-Templates.json'), false);

        $colourMap = [];
        foreach ($colourTemplates as $col) {
            $name = $col->ColourName ?? '';
            if ($name !== '') {
                $colourMap[$name] = $col->RGB ?? '';
            }
        }

        $skuByStyle = [];
        foreach ($skuList as $sku) {
            $style = $sku->Style ?? null;
            if ($style) {
                $skuByStyle[$style][] = $sku;
            }
        }

        $result = [];

        foreach ($products as $prod) {
            $style = $prod->Style ?? null;
            if (!$style) {
                continue;
            }

            $item = [
                'Style'                => $style,
                'Name1'                => $prod->Name1 ?? '',
                'Name2'                => $prod->{'Name2 (czech)'} ?? '',
                'Product-Description'  => $prod->{'Product-Description (czech)'} ?? '',
                'Material-Description' => $prod->{'Material-Description (czech)'} ?? '',
                'Categories'           => $prod->{'Categories (czech)'} ?? '',
                'Manufacturer'         => '',
                'PriceOneCZK'          => 0,
                'SizeColorSKUs'        => [],
                'Variants'             => [],
                'Sizes'                => []
            ];

            if (!empty($skuByStyle[$style])) {

                $allSizes    = [];
                $variantsMap = [];
                $priceByColor = [];
                $sizeColorSKUs = [];

                foreach ($skuByStyle[$style] as $sku) {
                    $price = floatval($sku->VKEinzel ?? 0);

                    $colorName       = $sku->Colour ?? '';
                    $firstColorName  = trim(explode('/', $colorName)[0]);
                    $hexColor        = $colourMap[$firstColorName] ?? '';

                    if (!isset($priceByColor[$firstColorName])) {
                        $priceByColor[$firstColorName] = ['sum' => 0.0, 'cnt' => 0];
                    }
                    if ($price > 0) {
                        $priceByColor[$firstColorName]['sum'] += $price;
                        $priceByColor[$firstColorName]['cnt']++;
                    }

                    if (!isset($variantsMap[$firstColorName])) {
                        $variantsMap[$firstColorName] = [
                            'Color'    => $firstColorName,
                            'Image'    => $sku->Packshot ?? '',
                            'RGBColor' => $hexColor
                        ];
                    }

                    $size = $sku->Size ?? '';
                    if ($size !== '' && !in_array($size, $allSizes, true)) {
                        $allSizes[] = $size;
                    }

                    if (!empty($sku->SKU)) {
                        $sizeColorSKUs[$sku->SKU] = [$hexColor, $size];
                    }
                }

                $colorAverages = [];
                foreach ($priceByColor as $p) {
                    if ($p['cnt']) {
                        $colorAverages[] = $p['sum'] / $p['cnt'];
                    }
                }

                if ($colorAverages) {
                    $item['PriceOneCZK'] = (int) round(array_sum($colorAverages) / count($colorAverages));
                }

                $item['Sizes']          = $allSizes;
                $item['Variants']       = array_values($variantsMap);
                $item['SizeColorSKUs']  = $sizeColorSKUs;

                $firstSku = $skuByStyle[$style][0] ?? null;
                if ($firstSku) {
                    $item['Manufacturer'] = $firstSku->Manufacturer ?? '';
                }
            }

            $result[] = (object) $item;
        }

        $directory = __DIR__ . '/../temp/catalogue';
        if (is_dir($directory)) {
            $files = array_diff(scandir($directory), ['.', '..']);
            array_map(fn($file) => unlink($directory . '/' . $file), $files);
            rmdir($directory);
        }

        return $result;
    }


    public function getProducts(): array
    {
        return json_decode(file_get_contents(__DIR__ . '/../temp/all_products.json'), false);
    }

    public function productCount()
    {
        return count($this->getProducts());
    }

    public function getProduct($index)
    {
        $products = $this->getProducts();

        if (isset($products[$index])) {
            return $products[$index];
        }

        return null;
    }

    public function removeProduct($index)
    {
        $products = $this->getProducts();
        $newProducts = [];

        foreach ($products as $prod) {
            if ($products[$index] !== $prod) {
                $newProducts[] = $prod;
            }
        }

        file_put_contents(__DIR__ . '/../temp/all_products.json', json_encode($newProducts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
