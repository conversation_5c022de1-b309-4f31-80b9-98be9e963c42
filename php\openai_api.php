<?php
/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

namespace CottonClassics\OpenAI_API;

use CottonClassics\Functions\Functions;

require_once(__DIR__ . '/functions.php');

class OpenAI_API
{
    private string   $apiKey;
    private Functions $functions;

    public function __construct()
    {
        $this->functions = new Functions();
        $config          = $this->functions->loadConfigFile('openai.conf');
        $this->apiKey    = $config['openai']['apikey'] ?? '';
    }

    /** Vrátí textovou odpověď */
    public function generateText(string $prompt, int $maxTokens = 500): string
    {
        $url  = 'https://api.openai.com/v1/chat/completions';
        $data = [
            'model'    => 'gpt-3.5-turbo',
            'messages' => [
                ['role' => 'system', 'content' => 'You are a endpoint creating product descriptions.'],
                ['role' => 'user',   'content' => $prompt],
            ],
            'max_tokens' => $maxTokens,
        ];

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
        ];

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST           => true,
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_POSTFIELDS     => json_encode($data, JSON_UNESCAPED_UNICODE),
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        $decoded = json_decode($response, true);
        if (isset($decoded['error'])) {
            return 'Chyba: ' . $decoded['error']['message'];
        }

        return $decoded['choices'][0]['message']['content'] ?? 'Chyba při generování textu.';
    }

    public function editImage(string $image_name, string $prompt, string $suffix = '_backai.png'): void
    {
        $imgPath = __DIR__ . '/../temp/pictures/' . $image_name;
        if (!is_file($imgPath)) {
            fwrite(STDERR, "Chyba: Soubor nenalezen: $image_name\n");
            exit(1);
        }

        $url   = 'https://api.openai.com/v1/images/edits';
        $cfile = curl_file_create($imgPath, mime_content_type($imgPath), $image_name);

        $post = [
            'model'  => 'gpt-image-1',
            'prompt' => $prompt,
            'n'      => 1,
            'image'  => $cfile,
            'size'   => '1024x1024',
        ];

        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Accept: application/json',
        ];

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST           => true,
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_POSTFIELDS     => $post,
        ]);

        $response = curl_exec($ch);
        if ($response === false) {
            fwrite(STDERR, "Chyba cURL: " . curl_error($ch) . "\n");
            curl_close($ch);
            exit(1);
        }
        curl_close($ch);

        $decoded = json_decode($response, true);
        if ($decoded === null) {
            fwrite(STDERR, "Chyba při parsování JSON odpovědi:\n$response\n");
            exit(1);
        }
        if (isset($decoded['error'])) {
            fwrite(STDERR, "Chyba OpenAI: " . $decoded['error']['message'] . "\n");
            exit(1);
        }
        if (empty($decoded['data'][0]['b64_json'])) {
            fwrite(STDERR, "Chybí base64 obrazová data v odpovědi:\n" . json_encode($decoded) . "\n");
            exit(1);
        }

        $imageData = base64_decode($decoded['data'][0]['b64_json'], true);
        if ($imageData === false) {
            fwrite(STDERR, "Chyba při base64 dekódování dat.\n");
            exit(1);
        }

        $info     = pathinfo($image_name);
        $outFile  = $info['filename'] . $suffix;
        $outPath  = __DIR__ . '/../temp/pictures/' . $outFile;

        if (file_put_contents($outPath, $imageData) === false) {
            fwrite(STDERR, "Chyba při zápisu vygenerovaného souboru: $outPath\n");
            exit(1);
        }

        $imageInfo = getimagesize($outPath);
        if ($imageInfo === false) {
            fwrite(STDERR, "Nelze načíst výstupní obrázek: $outPath\n");
            exit(1);
        }

        [$width, $height, $type] = $imageInfo;
        $max = 1000;

        if ($width > $max || $height > $max) {
            $scale = min($max / $width, $max / $height);
            $newW = (int) round($width * $scale);
            $newH = (int) round($height * $scale);

            switch ($type) {
                case IMAGETYPE_JPEG:
                    $src = imagecreatefromjpeg($outPath);
                    break;
                case IMAGETYPE_PNG:
                    $src = imagecreatefrompng($outPath);
                    imagealphablending($src, false);
                    imagesavealpha($src, true);
                    break;
                case IMAGETYPE_GIF:
                    $src = imagecreatefromgif($outPath);
                    break;
                default:
                    fwrite(STDERR, "Nepodporovaný typ obrázku: $outPath\n");
                    exit(1);
            }

            $dest = imagecreatetruecolor($newW, $newH);
            if ($type === IMAGETYPE_PNG) {
                imagealphablending($dest, false);
                imagesavealpha($dest, true);
                $transparent = imagecolorallocatealpha($dest, 0, 0, 0, 127);
                imagefill($dest, 0, 0, $transparent);
            }

            imagecopyresampled($dest, $src, 0, 0, 0, 0, $newW, $newH, $width, $height);

            switch ($type) {
                case IMAGETYPE_JPEG:
                    imagejpeg($dest, $outPath, 100);
                    break;
                case IMAGETYPE_PNG:
                    imagepng($dest, $outPath);
                    break;
                case IMAGETYPE_GIF:
                    imagegif($dest, $outPath);
                    break;
            }

            imagedestroy($src);
            imagedestroy($dest);
        }
    }
}