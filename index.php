<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

require_once(__DIR__ . '/php/wp_check_access.php');
wp_checkAccess();

header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cotton Classics Autoimport</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .line {
            border-top: 2px dashed black;
            margin: 20px 0;
        }
        .btn-custom {
            width: 200px;
            height: 70px;
        }
    </style>
</head>
<body>
    <!-- Loading overlay -->
    <div id="loadingOverlay" class="d-none position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 d-flex justify-content-center align-items-center" style="z-index: 999999;">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Načítání...</span>
        </div>
    </div>
    <!-- -->
    <div class="container mt-5 d-flex flex-column justify-content-center align-items-center" style="min-height: 100vh;">
        <h1>Cotton Classics Autoimport</h1>
        <h3>Vítejte v administračním rozhraní.</h3>

        <?php
        if(file_exists('temp/db.info')) {
            $data = json_decode(file_get_contents('temp/db.info'), false);
        }
        ?>
        <h5 class="mt-5">Stažená verze databáze: <?php echo htmlspecialchars(isset($data->version) ? $data->version : "Neznámé"); ?></h5>
        <h5>Čas posledního stažení databáze: <?php echo htmlspecialchars(isset($data->date) ? $data->date : "Neznámé"); ?></h5>
        
        <div class="line"></div>
        <div class="d-flex justify-content-center align-items-center">
            <button onclick="go();" class="btn btn-success btn-custom">Přejít na importování</button>
            <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Přejděte do rozhraní pro import produktů."></i>
        </div>
        
        <div class="line"></div>
        <div class="d-flex justify-content-center align-items-center">
            <button onclick="download();" class="btn btn-primary btn-custom">Stáhnout aktuální databázi produktů</button>
            <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Proběhne stažení a přepsání databáze všech produktů dodavatele Cotton Classics z jejich serveru. Tato akce obnoví vyřazené a již naimportované produkty, a budou se znovu zobrazovat v rozhraní."></i>
        </div>
        
        <div class="line"></div>
        <div class="d-flex justify-content-center align-items-center">
            <button onclick="skuremoval();" class="btn btn-danger btn-custom">Provést vyřazení produktů podle SKU</button>
            <i class="bi bi-question-circle ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Pokud jste stáhli novou databázi produktů, tato funkce zajistí, že z ní budou vyřazeny produkty, které se SKU shodují s již naimportovanými produkty, aby nepřekážely v rozhraní. Toto neodstraňuje WooCommerce produkty!"></i>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <script>
        var tooltipTriggerList = Array.from(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        function showLoading() {
            document.getElementById("loadingOverlay").classList.remove("d-none");
        }

        function hideLoading() {
            document.getElementById("loadingOverlay").classList.add("d-none");
        }

        function go() {
            showLoading();
            window.location.href = 'import.php';
        }

        function download() {
            let res = confirm("Opravdu chcete znovu stáhnout a přepsat databázi produktů?");
            if(res) {
                showLoading();
                fetch('download.php', {
                    method: 'GET',
                })
                .then(response => response.json())
                .then(result => {
                    hideLoading();

                    if (result.status === 'completed') {
                        alert('Databáze byla úspěšně stažena.');
                        location.reload();
                    } else {
                        alert('Při stahování databáze došlo k chybě. Kontaktuje IT technika.');
                    }
                })
                .catch(error => {
                    hideLoading();
                    alert('Při stahování databáze došlo k chybě. Kontaktuje IT technika.');
                });
            }
        }

        function skuremoval() {
            let res = confirm("Opravdu chcete provést vyřazení produktů z databáze produktů připravených na import na základě jejich SKU?");
            if(res) {
                showLoading();
                fetch('skuremoval.php', {
                    method: 'GET',
                })
                .then(response => response.json())
                .then(result => {
                    hideLoading();

                    if (result.status === 'completed') {
                        alert('Produkty se shodnými SKU byly úspěšně vyřazeny.');
                    } else {
                        alert('Při vyřazování produktů došlo k chybě. Kontaktuje IT technika.');
                    }
                })
                .catch(error => {
                    hideLoading();
                    alert('Při vyřazování produktů došlo k chybě. Kontaktuje IT technika.');
                });
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>