<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

namespace CottonClassics\Tshirtecommerce_API;

use CottonClassics\Functions\Functions;
require_once(__DIR__ . '/functions.php');

// T-shirt Databse Connection (DBC)
require_once(__DIR__ . '/../../tshirtecommerce/dbc/dbc.php');

class Tshirtecommerce_API {
    public function createProduct(
        string $sku,
        string $name,
        int $price,
        string $description,
        string $shortDescription,
        array  $images_front,
        array  $images_back,
        array  $images_left,
        array  $images_right,
        int    $minOrder,
        string $sizeImage,
        float  $salePrice,
        array  $attributeTitles,
        array  $prices,
        array  $colors
    )
    {
        $product = new \stdClass();
        
        $product->title             = $name;
        $product->description       = $description;
        $product->short_description = $shortDescription;
        $product->published         = 1;
        $product->sku               = $sku;
        $product->price             = (int)$price;
        $product->image             = $images_front[0];
        $product->print_type        = 'DTG99';
        $product->min_order         = (int)$minOrder;
        $product->max_oder          = 0;
        $product->tax               = 0;
        $product->sale_price        = (int)$salePrice;
        $product->size              = $sizeImage;
        $product->box_width         = 500;
        $product->box_height        = 500;
        $product->prices_variations = '';
        $product->dpioutput         = 71;
        
        $product->character                  = new \stdClass();
        $product->character->limit           = '';
        $product->character->capitalize      = '0';
        
        $product->productCheckItemFitFlg     = '';
        $product->productCheckItemFitType    = '';
        $product->theme                      = '';
        $product->download_mask              = 0;
        $product->allow_DTG99_printing       = 1;
        $product->allow_PEVNA_printing       = 1;
        $product->allow_SIZE_printing        = 1;
        $product->allow_ELEME_printing       = 1;
        $product->allow_SUB99_printing       = 1;
        $product->allow_show_printing_type   = 1;
        $product->allow_COLOR_printing       = 1;
        
        $product->product_layout_design                         = new \stdClass();
        $product->product_layout_design->show_product_info      = '1';
        $product->product_layout_design->show_product_size      = '1';
        $product->product_layout_design->show_change_product    = '1';
        $product->product_layout_design->show_add_text          = '1';
        $product->product_layout_design->show_add_art           = '1';
        $product->product_layout_design->show_upload            = '1';
        $product->product_layout_design->show_add_team          = '1';
        $product->product_layout_design->show_add_qrcode        = '1';
        $product->product_layout_design->show_color_used        = '1';
        $product->product_layout_design->show_screen_size       = '1';
        
        $product->hide_quickview         = 1;
        $product->show_price             = 1;
        $product->btn_price              = 1;
        $product->btn_add_cart           = 1;
        $product->textdefault_attribute  = 'Ahoj !';
        $product->colordefault_attribute = 'D40096';
        $product->gallery                = [];
        
        $product->attributes = new \stdClass();
        $product->attributes->name     = ['Velikost a počet'];
        $product->attributes->titles   = [$attributeTitles];
        $emptyPricesForTitles          = array_fill(0, count($attributeTitles), '');
        $product->attributes->prices   = [$emptyPricesForTitles];
        $product->attributes->type     = ['textlist'];
        $product->attributes->obj      = ['none'];
        $product->attributes->required = ['0'];
        $product->attributes->value    = [$emptyPricesForTitles];
        
        $product->fontdefault_attribute = '';
    
        $tshirt_products = json_decode(getProductsInJson(), false); 

        // ID Selection
        $used_ids = [];
        foreach ($tshirt_products->products as $selected_product) {
            if (isset($selected_product->id)) {
                $used_ids[] = $selected_product->id;
            }
        }

        $id = 1;
        while (in_array($id, $used_ids)) {
            $id++;
        }

        $product->id = $id;
        
        $product->prices = new \stdClass();
        if (
            isset($prices['min_quantity']) &&
            isset($prices['max_quantity']) &&
            isset($prices['price'])
        ) {
            $product->prices->min_quantity = $prices['min_quantity'];
            $product->prices->max_quantity = $prices['max_quantity'];
            $product->prices->price        = $prices['price'];
        } else {
            $product->prices->min_quantity = [1, 5, 20];
            $product->prices->max_quantity = [4, 19, 10000];
            $product->prices->price        = [219, 209, 199];
        }
        
        $product->art_full_area = '';
        
        $product->design = new \stdClass();
        $product->design->color_hex       = array_values($colors);
        $product->design->color_title     = array_keys($colors);
        $product->design->price           = array_fill(0, count($colors), '');
        $product->design->page_number     = '';
        $product->design->page_title      = 'Page';
        $product->design->max_page_number = '0';
        
        $product->view_label = new \stdClass();
        $product->view_label->front = '';
        $product->view_label->back  = '';
        $product->view_label->left  = '';
        $product->view_label->right = '';
    
        // FRONT IMAGES -->
        $frontItems = [];
        foreach ($images_front as $imgUrl) {
            list($width, $height) = getimagesize($imgUrl);

            $new_width = $width / 2;
            $new_height = $height / 2;

            $top = (500 - $new_height) / 2;
            $left = (500 - $new_width) / 2;

            $frontItems[] = sprintf(
                '[{\'id\':\'area-design\'},{\'id\':\'images-1\',\'width\':\'%dpx\',\'height\':\'%dpx\',\'top\':\'%.2fpx\',\'left\':\'%.2fpx\',\'zIndex\':\'auto\',\'img\':\'%s\',\'is_product\':1,\'ismask\':0,\'is_change_color\':0}]',
                $new_width, $new_height, $top, $left, $imgUrl
            );
        }
        
        $product->design->front        = $frontItems;
        $product->design->images_front = '[' . implode(',', array_map(function($url) {
            return "'" . addslashes($url) . "'"; 
        }, $images_front)) . ']';

        // <--- FRONT IMAGES

        // BACK IMAGES -->
        if(count($images_back) > 0) {
            $backItems = [];
            foreach ($images_back as $imgUrl) {
                list($width, $height) = getimagesize($imgUrl);
    
                $new_width = $width / 2;
                $new_height = $height / 2;
    
                $top = (500 - $new_height) / 2;
                $left = (500 - $new_width) / 2;
    
                $backItems[] = sprintf(
                    '[{\'id\':\'area-design\'},{\'id\':\'images-1\',\'width\':\'%dpx\',\'height\':\'%dpx\',\'top\':\'%.2fpx\',\'left\':\'%.2fpx\',\'zIndex\':\'auto\',\'img\':\'%s\',\'is_product\':1,\'ismask\':0,\'is_change_color\':0}]',
                    $new_width, $new_height, $top, $left, $imgUrl
                );
            }
            
            $product->design->back         = $backItems;
            $product->design->images_back = '[' . implode(',', array_map(function($url) {
                return "'" . addslashes($url) . "'"; 
            }, $images_back)) . ']';
        }
        else {
            $product->design->back         = [];
            $product->design->images_back  = '[]';
        }
        // <--- BACK IMAGES
    
        // LEFT IMAGES -->
        if(count($images_left) > 0) {
            $leftItems = [];
            foreach ($images_left as $imgUrl) {
                list($width, $height) = getimagesize($imgUrl);
    
                $new_width = $width / 2;
                $new_height = $height / 2;
    
                $top = (500 - $new_height) / 2;
                $left = (500 - $new_width) / 2;
    
                $leftItems[] = sprintf(
                    '[{\'id\':\'area-design\'},{\'id\':\'images-1\',\'width\':\'%dpx\',\'height\':\'%dpx\',\'top\':\'%.2fpx\',\'left\':\'%.2fpx\',\'zIndex\':\'auto\',\'img\':\'%s\',\'is_product\':1,\'ismask\':0,\'is_change_color\':0}]',
                    $new_width, $new_height, $top, $left, $imgUrl
                );
            }
            
            $product->design->left         = $leftItems;
            $product->design->images_left = '[' . implode(',', array_map(function($url) {
                return "'" . addslashes($url) . "'"; 
            }, $images_left)) . ']';
        }
        else {
            $product->design->left         = [];
            $product->design->images_left  = '[]';
        }
        // <--- LEFT IMAGES
        
        // RIGHT IMAGES -->
        if(count($images_right) > 0) {
            $rightItems = [];
            foreach ($images_right as $imgUrl) {
                list($width, $height) = getimagesize($imgUrl);
    
                $new_width = $width / 2;
                $new_height = $height / 2;
    
                $top = (500 - $new_height) / 2;
                $left = (500 - $new_width) / 2;
    
                $rightItems[] = sprintf(
                    '[{\'id\':\'area-design\'},{\'id\':\'images-1\',\'width\':\'%dpx\',\'height\':\'%dpx\',\'top\':\'%.2fpx\',\'left\':\'%.2fpx\',\'zIndex\':\'auto\',\'img\':\'%s\',\'is_product\':1,\'ismask\':0,\'is_change_color\':0}]',
                    $new_width, $new_height, $top, $left, $imgUrl
                );
            }
            
            $product->design->right         = $rightItems;
            $product->design->images_right = '[' . implode(',', array_map(function($url) {
                return "'" . addslashes($url) . "'"; 
            }, $images_right)) . ']';
        }
        else {
            $product->design->right         = [];
            $product->design->images_right  = '[]';
        }
        // <--- RIGHT IMAGES
    
        $product->design->params              = new \stdClass();
        $product->design->params->front       = '{\'page\':\'custom\',\'width\':\'24\',\'height\':\'29.13\',\'lockW\':false,\'lockH\':false,\'shape\':\'square\',\'shapeVal\':0}';
        $product->design->params->back        = '{\'page\':\'custom\',\'width\':\'24\',\'height\':\'29.13\',\'lockW\':false,\'lockH\':false,\'shape\':\'square\',\'shapeVal\':0}';
        $product->design->params->left        = '{\'page\':\'custom\',\'width\':\'24\',\'height\':\'29.13\',\'lockW\':false,\'lockH\':false,\'shape\':\'square\',\'shapeVal\':0}';
        $product->design->params->right       = '{\'page\':\'custom\',\'width\':\'24\',\'height\':\'29.13\',\'lockW\':false,\'lockH\':false,\'shape\':\'square\',\'shapeVal\':0}';
    
        $product->design->area                = new \stdClass();
        $product->design->area->front         = '{\'width\':325,\'height\':392,\'left\':\'87.5px\',\'top\':\'54px\',\'radius\':\'0px\',\'zIndex\':\'200\'}';
        $product->design->area->back          = '{\'width\':325,\'height\':392,\'left\':\'87.5px\',\'top\':\'54px\',\'radius\':\'0px\',\'zIndex\':\'200\'}';
        $product->design->area->left          = '{\'width\':325,\'height\':392,\'left\':\'87.5px\',\'top\':\'54px\',\'radius\':\'0px\',\'zIndex\':\'200\'}';
        $product->design->area->right         = '{\'width\':325,\'height\':392,\'left\':\'87.5px\',\'top\':\'54px\',\'radius\':\'0px\',\'zIndex\':\'200\'}';
    
        $tshirt_products->products[] = $product;
        insertProductsToDB(json_encode($tshirt_products, JSON_UNESCAPED_SLASHES));
    
        return $product->id;
    }
}