<?php
function loadConfig() {
    $config_file = __DIR__ . '/config/cotton_api.conf';
    if (!file_exists($config_file)) {
        die("Configuration file not found: $config_file");
    }
    return parse_ini_file($config_file, true);
}

function logDebug($message) {
    global $debugLog;
    if (!isset($debugLog)) {
        $debugLog = array();
    }
    $debugLog[] = "[" . date('Y-m-d H:i:s') . "] " . $message;
}

try {
    $root_path = $_SERVER['DOCUMENT_ROOT'];

    define('WP_USE_THEMES', false);
    require_once($root_path . '/wp-blog-header.php');
    require_once($root_path . '/wp-load.php');

    $config = loadConfig();

    if ($_GET['key'] != $config['cron_settings']['security_key']) {
        http_response_code(403);
        die("403 - Invalid key!");
    }

    function dbConnectPDO() {
        $config_file = __DIR__ . '/config/db.conf';
        if (!file_exists($config_file)) {
            die("Database configuration file not found: $config_file");
        }

        $config = parse_ini_file($config_file, true);
        $host = $config['database']['host'];
        $dbname = $config['database']['name'];
        $username = $config['database']['user'];
        $password = $config['database']['password'];

        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            return $pdo;
        } catch(PDOException $e) {
            die("Database Connect for COTTON CONNECT: Error connecting to database: " . $e->getMessage());
        }
    }

    function isFriday()   { return date("w") == 5; }
    function isSaturday() { return date("w") == 6; }

    function getPaidOrderIds() {
        date_default_timezone_set('Europe/Prague');

        $current_datetime = new DateTime();

        if (isFriday()) {              // pátek – konec 12:30, rozsah 24 h
            $current_datetime->setTime(12, 30, 0);
            $start_datetime = (clone $current_datetime)->modify('-1 day');
        } elseif (isSaturday()) {      // sobota – konec 14:30, rozsah –26 h (pátek 12:30)
            $current_datetime->setTime(14, 30, 0);
            $start_datetime = (clone $current_datetime)->modify('-26 hours');
        } else {                       // ostatní dny – konec 14:30, rozsah 24 h
            $current_datetime->setTime(14, 30, 0);
            $start_datetime = (clone $current_datetime)->modify('-1 day');
        }

        $args = array(
            'post_type'      => 'shop_order',
            'posts_per_page' => -1,
            'post_status'    => 'wc-processing',
            'orderby'        => 'date',
            'order'          => 'DESC',
            'meta_query'     => array(
                array(
                    'key'     => '_date_paid',
                    'value'   => array(
                        strtotime($start_datetime->format('Y-m-d H:i:s')),
                        strtotime($current_datetime->format('Y-m-d H:i:s'))
                    ),
                    'compare' => 'BETWEEN',
                    'type'    => 'NUMERIC',
                ),
            ),
        );

        $query  = new WP_Query($args);
        $orders = $query->get_posts();

        return wp_list_pluck($orders, 'ID');
    }

    function getNomen($local_id, $local_color, $local_size) {
        $conn = dbConnectPDO();
        $sql = "SELECT nomen FROM 6tkk_cotton_pair
                WHERE local_id=:local_id AND local_color=:local_color AND local_size=:local_size";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':local_id'   => $local_id,
            ':local_color'=> $local_color,
            ':local_size' => $local_size
        ]);

        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($result) == 1) {
            logDebug("Found Cotton SKU: {$result[0]['nomen']} for product_id: $local_id, color: $local_color, size: $local_size");
            return $result[0]['nomen'];
        } elseif (count($result) > 1) {
            logDebug("Multiple Cotton SKUs found for product_id: $local_id, color: $local_color, size: $local_size");
            return "";
        } else {
            logDebug("No Cotton SKU found for product_id: $local_id, color: $local_color, size: $local_size");
            return "";
        }
    }

    function checkAutoorder($id) {
        $conn = dbConnectPDO();
        $sql  = "SELECT COUNT(*) AS count FROM 6tkk_cotton_pair WHERE local_id = :product_id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([':product_id' => $id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return ($result['count'] > 0);
    }

    function getStatus($itemId) {
        return wc_get_order_item_meta($itemId, 'product_order_status');
    }

    function updateStatus($itemId, $status) {
        wc_update_order_item_meta($itemId, 'product_order_status', $status);
    }

    function getNote($itemId) {
        $poznamky = get_poznamky_by_item_id($itemId);
        return $poznamky['poznamka_k_naskladnovani'] ?? null;
    }

    function updateNote($itemId, $note) {
        $oldNote = getNote($itemId);
        $oldNote .= $note;
        update_poznamka_for_item_id($itemId , 'naskladnovani', $oldNote);
    }

    function getOrdersItems($orderIds) {
        $allItems = array();

        foreach ($orderIds as $orderId) {
            $order = wc_get_order($orderId);
            if (!$order) continue;

            foreach ($order->get_items() as $item_id => $item) {
                $product     = $item->get_product();
                $product_id  = $product->get_id();
                $custom_dsgn = wc_get_order_item_meta($item_id, 'custom_designer');
                $item_color  = $custom_dsgn['color_hex'] ?? '';
                $item_size   = $custom_dsgn['options'][0]['value'] ?? '';

                if ($item_size != "") {
                    foreach ($item_size as $size_key => $count) {
                        if ($count && $count != 0) {
                            $nomen = getNomen($product_id, $item_color, $size_key);
                            $allItems[] = [
                                'order_id'   => $orderId,
                                'item_id'    => $item_id,
                                'product_id' => $product_id,
                                'item_color' => $item_color,
                                'item_size'  => $size_key,
                                'count'      => $count,
                                'nomen'      => trim($nomen),
                            ];
                        }
                    }
                }
            }
        }
        return $allItems;
    }

    function onlyAutoorderItems($items) {
        foreach ($items as $k => $item) {
            if (!checkAutoorder($item['product_id'])) unset($items[$k]);
        }
        return $items;
    }

    function onlyNonorderedItems($items) {
        foreach ($items as $k => $item) {
            if (getStatus($item['item_id']) != "") unset($items[$k]);
        }
        return $items;
    }

    function createNomenList($items) {
        $nomens = array();
        $items  = onlyNonorderedItems(onlyAutoorderItems($items));

        foreach ($items as $item) {
            $nomen = $item['nomen'];
            $count = $item['count'];
            if ($nomen == "" || $count <= 0) continue;

            $found = false;
            foreach ($nomens as &$n) {
                if ($n['SKU'] == $nomen) {
                    $n['Quantity'] += $count;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $nomens[] = [
                    'Position' => count($nomens) + 1,
                    'SKU'      => $nomen,
                    'Quantity' => (int)$count,
                ];
            }
        }
        return $nomens;
    }

    function createItemsListForStatusChange($items) {
        $out = [];
        $items = onlyNonorderedItems(onlyAutoorderItems($items));
        foreach ($items as $item) {
            if ($item['nomen'] != "") $out[] = $item['item_id'];
        }
        return array_values(array_unique($out));
    }

    function createNoNomenItemsList($items) {
        $out = [];
        $items = onlyNonorderedItems(onlyAutoorderItems($items));
        foreach ($items as $item) {
            if ($item['nomen'] == "") $out[] = $item['item_id'];
        }
        return array_values(array_unique($out));
    }

    function sendOrderToCottonApi($positions) {
        $config = loadConfig();

        if (empty($positions)) {
            logDebug("Cotton API Order SKIPPED - No positions to order");
            return (object)['error' => 'No positions to order'];
        }

        logDebug("Cotton API Order - Preparing to send " . count($positions) . " positions");

        $url = $config['api']['base_url'] . '/api/Order';

        foreach (['mandator', 'customerid'] as $field) {
            if (empty($config['api'][$field])) {
                logDebug("Cotton API Order ERROR - Missing required field: $field");
                return (object)['error' => "Missing field $field"];
            }
        }

        $orderData = [
            'Mandator'           => (int)$config['api']['mandator'],
            'CustomerID'         => $config['api']['customerid'],
            'OrderedByGender'    => (int)$config['order_settings']['ordered_by_gender'],
            'OrderedByFirstname' => $config['order_settings']['ordered_by_firstname'],
            'OrderedByLastname'  => $config['order_settings']['ordered_by_lastname'],
            'DeliveryAddressLine1' => $config['delivery_address']['address_line1'],
            'DeliveryStreet'       => $config['delivery_address']['street'],
            'DeliveryPostcode'     => $config['delivery_address']['postcode'],
            'DeliveryCity'         => $config['delivery_address']['city'],
            'DeliveryCountry'      => $config['delivery_address']['country'],
            'Positions'            => []
        ];

        foreach ([
            'DeliverySalutation'         => $config['delivery_address']['salutation'],
            'DeliveryAddressLine2'       => $config['delivery_address']['address_line2'],
            'DeliveryAddressSupplementary'=> $config['delivery_address']['supplementary'],
            'Commission'                 => $config['order_details']['commission'],
            'YourReference'              => $config['order_details']['your_reference'],
            'Comment'                    => $config['order_details']['comment'],
            'ConsultationPhone'          => $config['order_details']['consultation_phone'],
            'ConsultationMail'           => $config['order_details']['consultation_mail']
        ] as $key => $val) {
            if (!empty($val) && $val !== '-') $orderData[$key] = $val;
        }

        foreach ($positions as $pos) {
            if (empty($pos['SKU']) || $pos['Quantity'] <= 0) continue;
            $orderData['Positions'][(string)$pos['Position']] = [
                'Position' => (string)$pos['Position'],
                'SKU'      => $pos['SKU'],
                'Quantity' => (int)$pos['Quantity']
            ];
        }

        if (empty($orderData['Positions'])) {
            logDebug("Cotton API Order ABORTED - No valid positions after validation");
            return (object)['error' => 'No valid positions after validation'];
        }

        $jsonData = json_encode($orderData);
        logDebug("Cotton API Order - Final JSON payload: $jsonData");

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => $jsonData,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_SSL_VERIFYPEER => 0,
            CURLOPT_USERPWD        => $config['api']['username'] . ':' . $config['api']['password'],
            CURLOPT_HTTPHEADER     => ['Content-Type: application/json']
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $err = curl_error($ch);
            curl_close($ch);
            logDebug("Cotton API Order CURL Error: $err");
            return (object)['error' => "CURL: $err"];
        }
        curl_close($ch);

        logDebug("Cotton API Order Response - HTTP Code: $httpCode");
        logDebug("Cotton API Order Response - Raw: " . substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : ''));

        if ($httpCode !== 200) {
            return (object)['error' => "HTTP Error: $httpCode", 'response' => $response];
        }

        $data = json_decode($response);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return (object)['error' => 'Invalid JSON response'];
        }

        if (isset($data->ProcessID)) logDebug("Cotton API Order SUCCESS - ProcessID: {$data->ProcessID}");
        return $data;
    }

    function generateRandomString($length) {
        $chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $out = '';
        for ($i = 0; $i < $length; $i++) {
            $out .= $chars[random_int(0, strlen($chars) - 1)];
        }
        return $out;
    }

    logDebug("=== COTTON AUTO-ORDER EXECUTION START ===");

    $paidOrders = getPaidOrderIds();
    logDebug("Found " . count($paidOrders) . " paid orders to process");

    $items = getOrdersItems($paidOrders);
    logDebug("Extracted " . count($items) . " items from orders");

    $nomens        = createNomenList($items);
    $items_ok      = createItemsListForStatusChange($items);
    $items_noNomen = createNoNomenItemsList($items);

    $order_result = empty($nomens)
                  ? (object)['error' => 'No valid nomenclatures found']
                  : sendOrderToCottonApi($nomens);

    if (isset($order_result->ProcessID)) {
        foreach ($items_ok as $id) {
            updateStatus((string)$id, "objednano");
            updateNote((string)$id, "\nAO: Objednáno automaticky.\n");
        }
        foreach ($items_noNomen as $id) {
            updateStatus((string)$id, "bezestavu");
            updateNote((string)$id, "\nAO: Varianta nemá spárovanou žádnou nomenklaturu.\n");
        }
    }


    date_default_timezone_set($config['cron_settings']['timezone']);

    $now = new DateTime();
    if (isFriday()) {
        $now->setTime(12, 30, 0);
        $start = (clone $now)->modify('-1 day');
    } elseif (isSaturday()) {
        $now->setTime(14, 30, 0);
        $start = (clone $now)->modify('-26 hours');
    } else {
        $now->setTime(14, 30, 0);
        $start = (clone $now)->modify('-1 day');
    }

    $startDate  = $start->format('d.m.Y H:i');
    $endDate    = $now->format('d.m.Y H:i');
    $logName    = $config['cron_settings']['log_directory'] . "/" . date("Ymd") . "_" . generateRandomString(5) . ".html";
    $formatDate = date("d.m.Y H:i");

    $log  = "<h1>COTTON CLASSICS Automatické objednání</h1>";
    $log .= "<h3><p>$formatDate ($startDate až $endDate)</p></h3>";

    if (isset($order_result->ProcessID)) {
        $oid = $order_result->OrderID ?? $order_result->ProcessID;
        $log .= "<h3><p>Order ID: $oid</p></h3>";
        http_response_code(200);
        echo "200 OK";
    } else {
        $log .= "<pre>";
        if (isset($order_result->error)) {
            $log .= "<h3 style=\"color:orange;\"><p>VAROVÁNÍ: " . htmlspecialchars($order_result->error) . "</p></h3>";
            http_response_code(empty($nomens) ? 200 : 500);
            echo empty($nomens) ? "200 OK - No items to order" : "500 ERROR";
        } else {
            $log .= "<h3 style=\"color:red;\"><p>CHYBA: " . print_r($order_result, true) . "</p></h3>";
            http_response_code(500);
            echo "500 ERROR";
        }
        $log .= "</pre>";
    }

    $log .= "<h2>DEBUG INFO:</h2>";
    $log .= "<h4>Seznam itemů z objednávek:</h4><pre>" . print_r($items, true) . "</pre>";
    $log .= "<h4>Seznam vytříděných SKU:</h4><pre>" . print_r($nomens, true) . "</pre>";
    $log .= "<h4>Úspěšně změněné položky:</h4><pre>" . print_r($items_ok, true) . "</pre>";
    $log .= "<h4>Položky bez nomenklatury:</h4><pre>" . print_r($items_noNomen, true) . "</pre>";

    global $debugLog;
    if (!empty($debugLog)) {
        $log .= "<h2>DEBUG LOG:</h2><pre>";
        foreach ($debugLog as $entry) {
            $log .= htmlspecialchars($entry) . "\n";
        }
        $log .= "</pre>";
    }

    file_put_contents($logName, $log);

} catch (Exception $e) {
    try {
        $config = loadConfig();
        $logDir = $config['cron_settings']['log_directory'];
    } catch (Exception $cfgErr) {
        $logDir = "log";
    }
    $logName = $logDir . "/" . date("Ymd") . "_" . generateRandomString(5) . "_logging_error.html";
    file_put_contents($logName, $e->getMessage());
}