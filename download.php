<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

require_once(__DIR__ . '/php/wp_check_access.php');
wp_checkAccess();

session_start();
$_SESSION['actual_position'] = 1;

require_once(__DIR__ . '/php/scraper.php');
require_once(__DIR__ . '/php/catalogue_handler.php');

use CottonClassics\Scraper\Scraper;
use CottonClassics\CatalogueHandler\CatalogueHandler;

$scraper = new Scraper();
$c_handler = new CatalogueHandler();

if(file_exists('temp/all_products.json')) {
    unlink('temp/all_products.json');
}

$version = $scraper->getProductCatalogue();

file_put_contents('temp/all_products.json', json_encode($c_handler->mergeProductData()));

if(file_exists('temp/db.info')) {
    unlink('temp/db.info');
}
$data = [
    'date' => date('Y-m-d H:i:s'),
    'version' => $version
];
file_put_contents('temp/db.info', json_encode($data));

$response = [
    'status' => 'completed'
];

http_response_code(200);
echo json_encode($response);