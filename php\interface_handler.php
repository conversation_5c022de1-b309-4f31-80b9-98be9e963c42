<?php

/*
Cotton Classics Autoimport
Made for Vytvor si potisk s.r.o.
By <PERSON> © 2025
*/

namespace CottonClassics\InterfaceHandler;
require_once(__DIR__ . '/woocommerce_api.php');
require_once(__DIR__ . '/catalogue_handler.php');
require_once(__DIR__ . '/openai_api.php');
require_once(__DIR__ . '/scraper.php');

class InterfaceHandler {
    private $wc_api;
    private $categories;
    private $c_handler;
    private $products_count;
    private $actual_product;
    private $openai_api;
    private $scraper;
    function __construct() {
        $this->wc_api = new \CottonClassics\WooCommerce_API\WooCommerce_API();
        $this->categories = $this->wc_api->getCategories();

        $this->c_handler = new \CottonClassics\CatalogueHandler\CatalogueHandler();
        $this->products_count = $this->c_handler->productCount();
        $this->actual_product = $this->c_handler->getProduct($_SESSION['actual_position'] - 1);

        $this->openai_api = new \CottonClassics\OpenAI_API\OpenAI_API();

        $this->scraper = new \CottonClassics\Scraper\Scraper();
    }

    public function html_ProductNumber() {
        return $_SESSION['actual_position'] . ' z celkových ' . $this->products_count;
    }

    public function html_ProductImage()
    {
        if (empty($this->actual_product->Variants)) {
            return '';
        }

        $productId   = $this->actual_product->Style;
        $cookieName  = 'ai_gen_' . md5((string) $productId);
        $cookieValue = $_COOKIE[$cookieName] ?? null;
        if ($cookieValue !== null) {
            setcookie($cookieName, '', time() - 3600, '/');
        }

        $forceRegen = (
            ($_GET['mode']   ?? '') === 'gen' &&
            ($_GET['design'] ?? '') === $this->actual_product->Style
        );

        $imagesToGenerate = [];

        foreach ($this->actual_product->Variants as $variant) {
            if (!file_exists('temp/pictures/' . $variant->Image)) {
                $this->scraper->getImageGUID($variant->Image);
            }

            $info  = pathinfo($variant->Image);
            $base  = $info['filename'];
            $back  = $base . '_backai.png';
            $left  = $base . '_leftai.png';

            $regenForThisVariant = $forceRegen && (($_GET['color'] ?? '') === $variant->RGBColor);

            if (!file_exists('temp/pictures/' . $back) || $regenForThisVariant) {
                $imagesToGenerate[] = [
                    'img'    => $variant->Image,
                    'prompt' => 'Create a back view of this exact product. Important is to keep the same canvas size and object position, centered, no cropping, full #FFFFFF white background.',
                    'suffix' => '_backai.png'
                ];
            }

            if (!file_exists('temp/pictures/' . $left) || $regenForThisVariant) {
                $imagesToGenerate[] = [
                    'img'    => $variant->Image,
                    'prompt' => 'Create a left view (back is facing left side) of this exact product. Important is to keep the same canvas size and object position, centered, no cropping, full #FFFFFF white background.',
                    'suffix' => '_leftai.png'
                ];
            }
        }

        $askUser = false;
        if (!empty($imagesToGenerate) && !$forceRegen) {
            if ($cookieValue === '0') {
                $imagesToGenerate = [];
            } elseif ($cookieValue !== '1') {
                $askUser          = true;
                $imagesToGenerate = [];
            }
        }

        if (!empty($imagesToGenerate)) {
            $maxParallel = 20;
            $running     = [];

            while (!empty($imagesToGenerate) || !empty($running)) {
                while (count($running) < $maxParallel && !empty($imagesToGenerate)) {
                    $task = array_shift($imagesToGenerate);

                    $cmd = 'php -r ' . escapeshellarg('
                        require "' . __DIR__ . '/openai_api.php";
                        $api = new \CottonClassics\OpenAI_API\OpenAI_API();
                        $api->editImage("' . addslashes($task['img']) . '", "' . addslashes($task['prompt']) . '", "' . addslashes($task['suffix']) . '");

                        if ("' . addslashes($task['suffix']) . '" === "_leftai.png") {
                            $info  = pathinfo("' . addslashes($task['img']) . '");
                            $dir   = __DIR__ . "/temp/pictures/";
                            $left  = $dir . $info["filename"] . "_leftai.png";
                            $right = $dir . $info["filename"] . "_rightai.png";

                            $src = imagecreatefrompng($left);
                            if ($src) {
                                $w = imagesx($src); $h = imagesy($src);
                                $flip = imagecreatetruecolor($w, $h);
                                imagecopyresampled($flip, $src, 0, 0, $w - 1, 0, $w, $h, -$w, $h);
                                imagepng($flip, $right);
                                imagedestroy($flip);
                                imagedestroy($src);
                            }
                        }
                    ');

                    $descriptors = [
                        0 => ['pipe', 'r'],
                        1 => ['pipe', 'w'],
                        2 => ['pipe', 'w'],
                    ];

                    $proc = proc_open($cmd, $descriptors, $pipes);
                    if (!is_resource($proc)) {
                        die("Chyba: Nelze spustit AI proces:\n$cmd");
                    }

                    $running[] = ['proc' => $proc, 'pipes' => $pipes, 'cmd' => $cmd];
                }

                foreach ($running as $i => $entry) {
                    if (!proc_get_status($entry['proc'])['running']) {
                        $stderr = stream_get_contents($entry['pipes'][2]);
                        fclose($entry['pipes'][0]);
                        fclose($entry['pipes'][1]);
                        fclose($entry['pipes'][2]);
                        $exitCode = proc_close($entry['proc']);
                        if ($exitCode !== 0) {
                            die("Chyba AI procesu ($exitCode):\nCMD: {$entry['cmd']}\n$stderr");
                        }
                        unset($running[$i]);
                    }
                }

                $running = array_values($running);
                usleep(200000); // 200 ms
            }
        }

        $html = '<div id="productImageCarousel" class="carousel" data-bs-ride="carousel" style="max-width: 350px;">
            <div class="carousel-inner">';

        foreach ($this->actual_product->Variants as $i => $variant) {
            $info = pathinfo($variant->Image);
            $base = $info['filename'];

            $files = [
                'orig'  => 'temp/pictures/' . $variant->Image,
                'back'  => 'temp/pictures/' . $base . '_backai.png',
                'left'  => 'temp/pictures/' . $base . '_leftai.png',
                'right' => 'temp/pictures/' . $base . '_rightai.png'
            ];

            foreach ($files as $key => $file) {
                if ($key === 'orig' || file_exists($file)) {
                    $src    = $file . '?v=' . filemtime($file);
                    $active = ($i === 0 && $key === 'orig') ? ' active' : '';
                    $html  .= '<div class="carousel-item' . $active . '">
                                <img src="' . htmlspecialchars($src) . '"
                                    class="d-block mx-auto"
                                    style="width:350px;height:350px;object-fit:contain;">
                            </div>';
                }
            }
        }

        $html .= '</div>
                <button class="carousel-control-prev" type="button" data-bs-target="#productImageCarousel" data-bs-slide="prev" style="filter:invert(100%);">
                <span class="carousel-control-prev-icon"></span>
                <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#productImageCarousel" data-bs-slide="next" style="filter:invert(100%);">
                <span class="carousel-control-next-icon"></span>
                <span class="visually-hidden">Next</span>
                </button>
                </div>';

        if ($askUser) {
            $productNameEsc = addslashes(trim(($this->actual_product->Name2 ?? '') . ' ' . ($this->actual_product->Name1 ?? '')));
            $html .= '<script>
    (function () {
        if (confirm("Chcete vygenerovat / dogenerovat AI obrázky pro\n\n' . $productNameEsc . '?")) {
            document.cookie = "' . $cookieName . '=1; path=/";
            showLoading();
            location.reload();
        } else {
            document.cookie = "' . $cookieName . '=0; path=/";
            showLoading();
            location.reload();
        }
    })();
    </script>';
        }

        return $html;
    }

    public function html_Categories() {
        $categories = $this->wc_api->getCategories();
        $productCategories = explode(',', $this->actual_product->Categories ?? '');
        $productCategories = array_map('trim', $productCategories);
        $foundMatch = false;
        $radioSelected = false;

        // Check if we're in regeneration mode and have saved categories
        $forceRegen = (
            ($_GET['mode']   ?? '') === 'gen' &&
            ($_GET['design'] ?? '') === $this->actual_product->Style
        );

        $savedCategories = [];
        $savedMainCategory = null;

        if ($forceRegen) {
            $productId = str_replace('.', '_', $this->actual_product->Style);
            $categoriesCookieName = 'categories_' . $productId;

            if (isset($_COOKIE[$categoriesCookieName])) {
                $cookieData = json_decode(urldecode($_COOKIE[$categoriesCookieName]), true);
                if ($cookieData && is_array($cookieData)) {
                    $savedCategories = $cookieData['categories'] ?? [];
                    $savedMainCategory = $cookieData['mainCategory'] ?? null;
                }
                // Delete the cookie after using it
                setcookie($categoriesCookieName, '', time() - 3600, '/');
            }
        }
    
        $render = function($categories, $isSub = false) use (&$render, &$foundMatch, $productCategories, &$radioSelected, $savedCategories, $savedMainCategory) {
            $html = $isSub ? '<div class="ms-3">' : '';
            foreach ($categories as $cat) {
                // Check if this category should be selected
                // First check saved categories from cookie (if regenerating), otherwise use default product categories
                $isChecked = !empty($savedCategories)
                    ? in_array($cat['id'], $savedCategories)
                    : in_array($cat['name'], $productCategories);

                if ($isChecked) {
                    $foundMatch = true;
                }
    
                $html .= '<div class="d-flex align-items-center mb-2">';
    
                $html .= '<div class="form-check">';
                $html .= '<input class="form-check-input" type="checkbox" id="cat_' . htmlspecialchars($cat['id']) . '" value="' . htmlspecialchars($cat['slug']) . '"' . ($isChecked ? ' checked' : '') . '>';
                $html .= '<label class="form-check-label fw-bold" for="cat_' . htmlspecialchars($cat['id']) . '">' . htmlspecialchars($cat['name']) . '</label>';
                $html .= '</div>';
    
                $html .= '<div class="ms-auto">';
                $radioChecked = '';

                // Check if this should be the main category
                // First check saved main category from cookie (if regenerating), otherwise use first checked category
                $shouldBeMainCategory = !empty($savedCategories)
                    ? ($savedMainCategory && $cat['id'] == $savedMainCategory)
                    : ($isChecked && !$radioSelected);

                if ($shouldBeMainCategory) {
                    $radioChecked = ' checked';
                    $radioSelected = true;
                }

                $html .= '<input class="form-check-input" type="radio" name="default_category" id="default_cat_' . htmlspecialchars($cat['id']) . '" value="' . htmlspecialchars($cat['slug']) . '"' . $radioChecked . '>';
                $html .= '</div>';
    
                $html .= '</div>';
    
                if (!empty($cat['children'])) {
                    $html .= $render($cat['children'], true);
                }
            }
            $html .= $isSub ? '</div>' : '';
            return $html;
        };
    
        $html = $render($categories);
    
        if (!$foundMatch) {
            $html = preg_replace_callback(
                '/(<input class="form-check-input" type="checkbox" id="cat_([^"]+)" value="([^"]+)")/',
                function($matches) use (&$html) {
                    $html = preg_replace(
                        '/(<input class="form-check-input" type="radio" name="default_category" id="default_cat_' . preg_quote($matches[2], '/') . '" value="' . preg_quote($matches[3], '/') . '")/',
                        '$1 checked',
                        $html,
                        1
                    );
                    return $matches[1] . ' checked';
                },
                $html,
                1
            );
        }
    
        return $html;
    }

    public function html_TitlePriceSalePrice() {
        $actual_price = $this->actual_product->PriceOneCZK;

        $new_sale_price = 0;

        if ($actual_price <= 100) {
            $new_sale_price = $actual_price * 1.9;
        } else if ($actual_price > 100 && $actual_price <= 300) {
            $new_sale_price = $actual_price * 1.7;
        } else if ($actual_price > 300 && $actual_price <= 1000) {
            $new_sale_price = $actual_price * 1.67;
        } else if ($actual_price > 1000) {
            $new_sale_price = $actual_price * 1.55;
        }

        $new_price = $new_sale_price * 1.2;

        $rounded_sale_price = ceil($new_sale_price);
        while ($rounded_sale_price % 10 !== 9) {
            $rounded_sale_price++;
        }

        return '
            <h4 class="me-3">Název:</h4>
            <input id="title" type="text" style="font-size: 1.25rem; width: 540px;" value="' . htmlspecialchars($this->actual_product->Name2 . ' ' . $this->actual_product->Name1) . '">
            <h4 class="me-3 ms-4">Cena:</h4>
            <input id="price" type="text" style="font-size: 1.25rem; width: 80px;" value="' . round($new_price) . '">
            <h4 class="me-3 ms-4">Cena po slevě:</h4>
            <input id="price_sale" type="text" style="font-size: 1.25rem; width: 80px;" value="' . $rounded_sale_price . '">
        ';
    }

    public function html_WCSKU() {
        $sku_used = $this->wc_api->SKUAlreadyUsed($this->actual_product->Style);
    
        if ($sku_used) {
            $sku_style = 'color: red; font-size: 1.25rem; width: 180px;';
            $sku_label_style = 'color: red; animation: blink 0.2s infinite;';
            $message = '<h5 style="color: red; margin-left: 40px;">Již existuje produkt se shodným Cotton SKU! Jste si jisti že chcete provést import?</h5>';
        } else {
            $sku_style = 'font-size: 1.25rem; width: 180px;';
            $sku_label_style = '';
            $message = '';
        }
    
        $blink_animation = '
            <style>
                @keyframes blink {
                    0% { opacity: 1; }
                    50% { opacity: 0; }
                    100% { opacity: 1; }
                }
            </style>
        ';
    
        return $blink_animation . '
            <div class="d-flex align-items-center">
                <h4 class="me-3" style="' . $sku_label_style . '">SKU:</h4>
                <input id="sku" type="text" style="' . $sku_style . '" value="' . htmlspecialchars($this->actual_product->Style) . '">
            </div>
            ' . $message;
    }       

    public function html_ShortDescLongDesc() {
        // Check if we're in regeneration mode and have a saved short description
        $forceRegen = (
            ($_GET['mode']   ?? '') === 'gen' &&
            ($_GET['design'] ?? '') === $this->actual_product->Style
        );

        $productId = str_replace('.', '_', $this->actual_product->Style);
        $wcShortCookieName = 'wc_short_' . $productId;
        $savedShortDesc = null;

        if ($forceRegen && isset($_COOKIE[$wcShortCookieName])) {
            $savedShortDesc = urldecode($_COOKIE[$wcShortCookieName]);
            // Delete the cookie after using it
            setcookie($wcShortCookieName, '', time() - 3600, '/');
        }

        // Use saved description if available, otherwise generate new one
        $short_description = $savedShortDesc ?? $this->openai_api->generateText("Vygeneruj zhruba 350 znakový popis oblečení určeného pro potisk, neuváděj žádné techické detaily ani název produktu, zaujmi zákazníka: " . $this->actual_product->Name2 . ': ' . $this->actual_product->{'Product-Description'});

        /* Long description */
        $descParts     = preg_split('/\s*,\s*/', $this->actual_product->{'Product-Description'});
        $materialParts = preg_split('/\s*,\s*/', $this->actual_product->{'Material-Description'});

        $allParts = array_merge($descParts, $materialParts);
        $allParts = array_filter(array_map('trim', $allParts), static fn($p) => $p !== '');

        $formatted = array_map(
            static fn($p) => '<div style="margin:0">✅ ' . htmlspecialchars($p, ENT_QUOTES, 'UTF-8') . '</div>',
            $allParts
        );

        $long_description = '<br>' . implode('', $formatted);
        /* End */

        return '
            <link rel="stylesheet" href="https://unpkg.com/pell/dist/pell.min.css">
            <script src="https://unpkg.com/pell"></script>

            <div class="d-flex align-items-start">
                <div class="d-flex flex-column me-4">
                    <h4 class="mb-2 text-start">WC Krátký popis:</h4>
                    <textarea id="short_description" rows="6" style="font-size: 1.25rem; width: 520px; height: 330px;">' . htmlspecialchars($short_description) . '</textarea>
                </div>

                <div class="d-flex flex-column">
                    <h4 class="mb-2 text-start">WC popis produktu:</h4>
                    <div id="long_description_editor" class="pell" style="width: 650px;"></div>
                    <input type="hidden" name="long_description" id="long_description">
                </div>
            </div>

            <style>
                .pell-content {
                    text-align: left;
                }
            </style>

            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const editor = pell.init({
                        element: document.getElementById("long_description_editor"),
                        onChange: function (html) {
                            document.getElementById("long_description").value = html;
                        },
                        actions: [
                            "bold",
                            "italic",
                            "underline",
                            "strikethrough",
                            "heading1",
                            "heading2",
                            "paragraph",
                            "quote",
                            "olist",
                            "ulist",
                            "line",
                            "link"
                        ]
                    });

                    const initialContent = ' . json_encode('<h2>' . $this->actual_product->Name2 . ' ' . $this->actual_product->Name1 . ' s vlastním potiskem online</h2>' . $long_description) . ';
                    editor.content.innerHTML = initialContent;
                    document.getElementById("long_description").value = initialContent;
                });
            </script>
        ';
    }

    public function html_SEOTitleSEOWord() {
        $seo_title = 'Vlastní potisk na ' . $this->actual_product->Name2 . ' ' . $this->actual_product->Name1;
        $seo_words = $this->actual_product->Name2 . ' ' . $this->actual_product->Name1;

        return '
            <!-- Rank Math SEO Title -->
            <tr style="height: 60px;">
                <td style="padding-right: 20px; text-align: left; vertical-align: middle;">
                    <h4 style="margin: 0;">Rank Math SEO TITLE:</h4>
                </td>
                <td>
                    <input id="seo_title" type="text" style="font-size: 1.25rem; width: 580px; vertical-align: middle;" 
                           value="' . htmlspecialchars($seo_title) . '">
                </td>
            </tr>
            <!-- Rank Math Klíčové slovo -->
            <tr style="height: 60px;">
                <td style="padding-right: 20px; text-align: left; vertical-align: middle;">
                    <h4 style="margin: 0;">Rank Math Klíčové slovo:</h4>
                </td>
                <td>
                    <input id="seo_word" type="text" style="font-size: 1.25rem; width: 580px; vertical-align: middle;" 
                           value="' . htmlspecialchars($seo_words) . '">
                </td>
            </tr>
        ';
    }

    public function html_TshirtColors() {
        $html = '<div class="d-flex flex-wrap justify-content-center gap-4">';

        if (!empty($this->actual_product->Variants)) {
            foreach ($this->actual_product->Variants as $variant) {
                $colorName = htmlspecialchars($variant->Color);
                $rgbColor  = "#" . htmlspecialchars($variant->RGBColor);

                $info     = pathinfo($variant->Image);
                $base     = $info['filename'];
                $dir      = 'temp/pictures/';
                $backImg  = $dir . $base . '_backai.png';
                $leftImg  = $dir . $base . '_leftai.png';

                $html .= '
                    <div class="d-flex flex-column align-items-center">
                        <input type="checkbox" class="form-check-input mb-1" id="colorcheckbox_' . $colorName . '" checked>
                        <div style="width: 80px; height: 80px; background-color: ' . $rgbColor . '; border-radius: 8px; border: 1px solid black;"></div>
                        <div class="d-flex justify-content-center my-1">';

                if (file_exists($backImg)) {
                    $html .= '<div style="border-radius: 8px; border: 1px solid black;"><img src="' . htmlspecialchars($backImg) . '?v=' . filemtime($backImg) . '" style="width:80px;height:80px;object-fit:contain;margin:10px;"></div>';
                }
                if (file_exists($leftImg)) {
                    $html .= '<div style="margin-left: 5px; border-radius: 8px; border: 1px solid black;"><img src="' . htmlspecialchars($leftImg) . '?v=' . filemtime($leftImg) . '" style="width:80px;height:80px;object-fit:contain;margin:10px;"></div>';
                }

                $html .= '</div>
                        <button class="btn btn-outline-primary btn-sm mt-1" onclick="gen(\'' . $this->actual_product->Style . '\', \'' . $variant->RGBColor . '\');">🔁 GEN</button>
                    </div>';
            }
        }

        return $html;
    }

    public function html_TshirtSizes() {
        $html = '<div class="d-flex flex-wrap justify-content-center gap-4">';
    
        if (!empty($this->actual_product->Sizes)) {
            foreach ($this->actual_product->Sizes as $size) {
                $sizeFormatted = htmlspecialchars($size);
    
                $html .= '
                    <div class="d-flex flex-column align-items-center">
                        <input type="checkbox" class="form-check-input mb-1" id="sizecheckbox_' . $sizeFormatted . '" checked>
                        <input id="sizetext_' . $sizeFormatted . '"  type="text" value="' . $sizeFormatted . '" 
                            class="d-flex align-items-center justify-content-center text-center autosize"
                            style="min-width: 50px; height: 50px; background-color: lightgray; border-radius: 8px; font-weight: bold; border: none; font-size: 1rem;">
                    </div>';
            }
        }
    
        $html .= '</div>
            <script>
                document.querySelectorAll(".autosize").forEach(function(input) {
                    function resize() {
                        input.style.width = "auto";
                        input.style.width = (input.scrollWidth + 5) + "px";
                    }
                    input.addEventListener("input", resize);
                    resize();
                });
            </script>';
        
        return $html;
    }

    public function html_SizeTableFile() {
        $imagePath = 'temp/sizetable_pictures/' . $this->actual_product->Style . '.jpg';
    
        if (!file_exists($imagePath)) {
            $this->scraper->getImageSizeTableStyleID($this->actual_product->Style);
        }
    
        $html = '
            <h4 class="mb-3 text-start">T-shirt Tabulka velikostí:</h4>';
    
        if (file_exists($imagePath)) {
            $html .= '
                <img id="imagePreview" class="mb-2 border rounded" style="max-width: 740px; max-height: 700px;" alt="Náhled obrázku" src="' . htmlspecialchars($imagePath) . '">
                <div id="dropArea" 
                    class="d-flex align-items-center justify-content-center border border-secondary rounded p-4"
                    style="width: 740px; height: 45px; background-color: #f8f9fa; cursor: pointer; text-align: center; transition: background-color 0.3s;">
                    Oficiálně dodaný obrázek, klikněte pro změnu
                </div>
                <input type="file" id="fileUpload" class="d-none" accept="image/*">';
        } else {
            $html .= '
                <img id="imagePreview" class="mb-2 border rounded d-none" style="max-width: 740px; max-height: 700px;" alt="Náhled obrázku">
                <div id="dropArea" 
                    class="d-flex align-items-center justify-content-center border border-secondary rounded p-4"
                    style="width: 740px; height: 45px; background-color: #f8f9fa; cursor: pointer; text-align: center; transition: background-color 0.3s;">
                    Přetáhnout nebo kliknout sem
                </div>
                <input type="file" id="fileUpload" class="d-none" accept="image/*">';
        }
    
        $html .= '
            <script>
                document.addEventListener("DOMContentLoaded", function() {
                    const fileInput = document.getElementById("fileUpload");
                    const dropArea = document.getElementById("dropArea");
                    const imagePreview = document.getElementById("imagePreview");
                    
                    dropArea.addEventListener("click", () => fileInput.click());
                    fileInput.addEventListener("change", handleFile);
                    
                    dropArea.addEventListener("dragover", (event) => {
                        event.preventDefault();
                        dropArea.classList.add("border-primary");
                        dropArea.style.backgroundColor = "#e9ecef";
                    });
    
                    dropArea.addEventListener("dragleave", () => {
                        dropArea.classList.remove("border-primary");
                        dropArea.style.backgroundColor = "#f8f9fa";
                    });
    
                    dropArea.addEventListener("drop", (event) => {
                        event.preventDefault();
                        dropArea.classList.remove("border-primary");
                        dropArea.style.backgroundColor = "#f8f9fa";
                        if (event.dataTransfer.files.length > 0) {
                            fileInput.files = event.dataTransfer.files;
                            handleFile();
                        }
                    });
    
                    function handleFile() {
                        const file = fileInput.files[0];
    
                        if (file) {
                            if (file.type.startsWith("image/")) {
                                dropArea.textContent = `${file.name}`;
    
                                const reader = new FileReader();
                                reader.onload = function(e) {
                                    imagePreview.src = e.target.result;
                                    imagePreview.classList.remove("d-none");
                                };
                                reader.readAsDataURL(file);
                            } else {
                                alert("Prosím nahrajte pouze obrázek!");
                                fileInput.value = "";
                                dropArea.textContent = "Přetáhnout nebo kliknout sem";
                                imagePreview.classList.add("d-none");
                            }
                        } else {
                            dropArea.textContent = "Přetáhnout nebo kliknout sem";
                            imagePreview.classList.add("d-none");
                        }
                    }
                });
            </script>';
    
        return $html;
    }

    public function html_QuantityPriceTable() {

        $original_price = $this->actual_product->PriceOneCZK;

        if ($original_price <= 100) {
            $new_sale_price = $original_price * 1.9;
        } else if ($original_price > 100 && $original_price <= 300) {
            $new_sale_price = $original_price * 1.7;
        } else if ($original_price > 300 && $original_price <= 1000) {
            $new_sale_price = $original_price * 1.67;
        } else {
            $new_sale_price = $original_price * 1.55;
        }

        $new_price = $new_sale_price * 1.2;

        $rounded_sale_price = ceil($new_sale_price);
        while ($rounded_sale_price % 10 !== 9) {
            $rounded_sale_price++;
        }

        if ($original_price <= 100) {
            $rows = [
                ['from' => 1, 'to' => 3, 'price' => $rounded_sale_price],
                ['from' => 4, 'to' => 8, 'price' => round($original_price * 1.8)],
                ['from' => 9, 'to' => 20, 'price' => round($original_price * 1.7)],
                ['from' => 21, 'to' => 40, 'price' => round($original_price * 1.62)],
                ['from' => 41, 'to' => 80, 'price' => round($original_price * 1.55)],
                ['from' => 81, 'to' => 200, 'price' => round($original_price * 1.45)],
                ['from' => 201, 'to' => 10000, 'price' => round($original_price * 1.35)],
            ];
        } else if ($original_price > 100 && $original_price <= 300) {
            $rows = [
                ['from' => 1, 'to' => 3, 'price' => $rounded_sale_price],
                ['from' => 4, 'to' => 8, 'price' => round($original_price * 1.66)],
                ['from' => 9, 'to' => 20, 'price' => round($original_price * 1.62)],
                ['from' => 21, 'to' => 40, 'price' => round($original_price * 1.58)],
                ['from' => 41, 'to' => 80, 'price' => round($original_price * 1.5)],
                ['from' => 81, 'to' => 200, 'price' => round($original_price * 1.41)],
                ['from' => 201, 'to' => 10000, 'price' => round($original_price * 1.32)],
            ];
        } else if ($original_price > 300 && $original_price <= 1000) {
            $rows = [
                ['from' => 1, 'to' => 3, 'price' => $rounded_sale_price],
                ['from' => 4, 'to' => 8, 'price' => round($original_price * 1.63)],
                ['from' => 9, 'to' => 20, 'price' => round($original_price * 1.56)],
                ['from' => 21, 'to' => 40, 'price' => round($original_price * 1.51)],
                ['from' => 41, 'to' => 80, 'price' => round($original_price * 1.46)],
                ['from' => 81, 'to' => 200, 'price' => round($original_price * 1.39)],
                ['from' => 201, 'to' => 10000, 'price' => round($original_price * 1.30)],
            ];
        } else if ($original_price > 1000) {
            $rows = [
                ['from' => 1, 'to' => 3, 'price' => $rounded_sale_price],
                ['from' => 4, 'to' => 8, 'price' => round($original_price * 1.52)],
                ['from' => 9, 'to' => 20, 'price' => round($original_price * 1.48)],
                ['from' => 21, 'to' => 40, 'price' => round($original_price * 1.45)],
                ['from' => 41, 'to' => 80, 'price' => round($original_price * 1.42)],
                ['from' => 81, 'to' => 200, 'price' => round($original_price * 1.39)],
                ['from' => 201, 'to' => 10000, 'price' => round($original_price * 1.35)],
            ];
        }
    
        $html = '
        <table class="table">
            <thead>
                <tr>
                    <th>Od</th>
                    <th>Do</th>
                    <th>Cena</th>
                    <th>Odstranit</th>
                </tr>
            </thead>
            <tbody id="pricingTable">';
    
        foreach ($rows as $index => $row) {
            $html .= '
            <tr>
                <td><input type="number" class="form-control" id="quantitypricefrom_' . ($index + 1) . '" value="' . htmlspecialchars($row['from']) . '"></td>
                <td><input type="number" class="form-control" id="quantitypriceto_' . ($index + 1) . '" value="' . htmlspecialchars($row['to']) . '"></td>
                <td><input type="number" class="form-control" id="quantityprice_' . ($index + 1) . '" value="' . htmlspecialchars($row['price']) . '"></td>
                <td><button class="btn btn-danger btn-sm" onclick="removeRow(this)">Odstranit</button></td>
            </tr>';
        }
    
        $html .= '
            </tbody>
        </table>';
    
        $html .= '
        <script>
            function addRow() {
                const table = document.getElementById("pricingTable");
                const row = document.createElement("tr");
    
                const rowCount = table.rows.length + 1;
    
                row.innerHTML = `
                    <td><input type="number" class="form-control" id="quantitypricefrom_${rowCount}"></td>
                    <td><input type="number" class="form-control" id="quantitypriceto_${rowCount}"></td>
                    <td><input type="number" class="form-control" id="quantityprice_${rowCount}"></td>
                    <td><button class="btn btn-danger btn-sm" onclick="removeRow(this)">Odstranit</button></td>
                `;
    
                table.appendChild(row);
            }
    
            function removeRow(button) {
                button.closest("tr").remove();
            }
        </script>';
    
        return $html;
    }
}